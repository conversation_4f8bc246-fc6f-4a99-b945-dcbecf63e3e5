package com.linkcircle.boss.module.billing.web.cache.api;

import com.linkcircle.boss.framework.common.enums.RpcConstants;
import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.module.billing.api.cache.BillingCacheUpdateApi;
import com.linkcircle.boss.module.billing.web.cache.sender.CacheUpdateMessageSender;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2025-07-24 10:50
 * @description 缓存更新RPC API控制器
 */
@RestController
@RequestMapping(RpcConstants.RPC_API_PREFIX + "/cache")
@Slf4j
@RequiredArgsConstructor
public class BillingCacheUpdateRpcApiController implements BillingCacheUpdateApi {

    private final CacheUpdateMessageSender cacheUpdateMessageSender;

    @Override
    public CommonResult<Void> sendCacheUpdate(String businessType, String businessId) {
        cacheUpdateMessageSender.sendCacheUpdateMessage(businessType, businessId);
        log.info("发送缓存更新通知成功: businessType={}, businessId={}", businessType, businessId);
        return CommonResult.success();
    }
}
