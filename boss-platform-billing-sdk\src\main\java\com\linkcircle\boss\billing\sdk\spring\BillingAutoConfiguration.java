package com.linkcircle.boss.billing.sdk.spring;

import com.linkcircle.boss.billing.sdk.BillingClient;
import com.linkcircle.boss.billing.sdk.config.BillingConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PreDestroy;

/**
 * <AUTHOR>
 * @date 2025-07-25 15:00
 * @description 计费SDK Spring Boot自动配置
 */
@Configuration
@ConditionalOnClass(BillingClient.class)
@ConditionalOnProperty(prefix = "billing.sdk", name = "enabled", havingValue = "true", matchIfMissing = true)
@EnableConfigurationProperties(BillingProperties.class)
public class BillingAutoConfiguration {

    private static final Logger logger = LoggerFactory.getLogger(BillingAutoConfiguration.class);

    private BillingClient billingClient;

    @Bean
    @ConditionalOnMissingBean
    public BillingConfig billingConfig(BillingProperties properties) {
        logger.info("创建计费SDK配置, baseUrl: {}, appId: {}", properties.getBaseUrl(), properties.getAppId());
        
        return BillingConfig.builder()
                .baseUrl(properties.getBaseUrl())
                .appId(properties.getAppId())
                .appSecret(properties.getAppSecret())
                .tenantId(properties.getTenantId())
                .connectTimeout(properties.getConnectTimeout())
                .readTimeout(properties.getReadTimeout())
                .writeTimeout(properties.getWriteTimeout())
                .maxRetries(properties.getMaxRetries())
                .debug(properties.isDebug())
                .build();
    }

    @Bean
    @ConditionalOnMissingBean
    public BillingClient billingClient(BillingConfig billingConfig) {
        logger.info("创建计费SDK客户端");
        this.billingClient = new BillingClient(billingConfig);
        return this.billingClient;
    }

    @PreDestroy
    public void destroy() {
        if (billingClient != null) {
            logger.info("关闭计费SDK客户端");
            billingClient.close();
        }
    }
}
