package com.linkcircle.boss.module.charge.fee.web.bill.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSONObject;
import com.linkcircle.boss.framework.common.exception.ErrorCode;
import com.linkcircle.boss.framework.common.exception.util.ServiceExceptionUtil;
import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.framework.common.model.PageResult;
import com.linkcircle.boss.framework.common.util.date.DateUtils;
import com.linkcircle.boss.framework.sharding.algorithms.dto.HitBusinessTimeDTO;
import com.linkcircle.boss.framework.tanant.TenantUtils;
import com.linkcircle.boss.module.billing.api.bill.product.model.dto.BillDiscountConfigDTO;
import com.linkcircle.boss.module.charge.fee.enums.InvoiceEnum;
import com.linkcircle.boss.module.charge.fee.web.bill.convert.InvoiceBillConvert;
import com.linkcircle.boss.module.charge.fee.web.bill.model.dto.BillReqDto;
import com.linkcircle.boss.module.charge.fee.web.bill.model.dto.BillReqHistoryDto;
import com.linkcircle.boss.module.charge.fee.web.bill.model.dto.InvoiceBillReqDto;
import com.linkcircle.boss.module.charge.fee.web.bill.model.dto.fee.FixedRateConfigFeeDTO;
import com.linkcircle.boss.module.charge.fee.web.bill.model.dto.fee.PackageRateConfigFeeDTO;
import com.linkcircle.boss.module.charge.fee.web.bill.model.dto.fee.TierRateConfigFeeDTO;
import com.linkcircle.boss.module.charge.fee.web.bill.model.dto.fee.UsageBasedRateConfigFeeDTO;
import com.linkcircle.boss.module.charge.fee.web.bill.model.vo.history.IncomeBillHistoryVO;
import com.linkcircle.boss.module.charge.fee.web.invoice.model.dto.BillInvoiceAmountCancelDTO;
import com.linkcircle.boss.module.charge.fee.web.invoice.model.dto.BillInvoiceCheckDTO;
import com.linkcircle.boss.module.charge.fee.web.invoice.model.dto.BillInvoiceCreateReqDTO;
import com.linkcircle.boss.module.crm.api.customer.customer.CustomerApi;
import com.linkcircle.boss.module.crm.api.customer.customer.vo.ChargeCustomerAccountsInfo;
import com.linkcircle.boss.module.crm.api.entity.EntityApi;
import com.linkcircle.boss.module.crm.api.entity.vo.EntityDetailsVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shardingsphere.infra.hint.HintManager;
import org.slf4j.Logger;

import java.math.BigDecimal;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.function.Predicate;

/**
 * <AUTHOR>
 * @date 2025/7/3 11:12
 */
public interface BillService<V> {

    InvoiceEnum.BillType getBillType();


    Function<String, Long> strToLong = s -> {
        if (StringUtils.isNotBlank(s)) {
            return DateUtil.parse(s, DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND).getTime();
        }
        return null;
    };

    default <R> R executeIgnore(Long start, Long end, List<String> serviceCodes, String logicTable, Callable<R> callable) {
        if (getBillType() == InvoiceEnum.BillType.PREPAID) {
            // 指定时间范围 只能有两个元素
            HitBusinessTimeDTO businessTimeDTO = HitBusinessTimeDTO.build(List.of(
                    start,
                    end
            ), serviceCodes);
            try (HintManager hintManager = HintManager.getInstance()) {
                hintManager.addTableShardingValue(logicTable, businessTimeDTO);
                // 查询预付费收入账单详情分页信息
                return TenantUtils.executeIgnore(callable);
            }
        } else {
            return TenantUtils.executeIgnore(callable);
        }
    }

    default <R> R executeIgnore(Callable<R> callable) {
        return TenantUtils.executeIgnore(callable);
    }

    default <R> R executeIgnore(Long time, String serviceCode, String logicTable, Callable<R> callable) {
        if (getBillType() == InvoiceEnum.BillType.PREPAID) {
            // 指定时间范围 只能有两个元素
            HitBusinessTimeDTO businessTimeDTO = HitBusinessTimeDTO.build(List.of(
                    time, time
            ), List.of(serviceCode));
            try (HintManager hintManager = HintManager.getInstance()) {
                hintManager.addTableShardingValue(logicTable, businessTimeDTO);
                // 查询预付费收入账单详情分页信息
                return TenantUtils.executeIgnore(callable);
            }
        } else {
            return TenantUtils.executeIgnore(callable);
        }
    }

    default <R> R executeIgnore(Long time, List<String> serviceCodes, String logicTable, Callable<R> callable) {
        if (getBillType() == InvoiceEnum.BillType.PREPAID) {
            // 指定时间范围 只能有两个元素
            HitBusinessTimeDTO businessTimeDTO = HitBusinessTimeDTO.build(List.of(
                    time, time
            ), serviceCodes);
            try (HintManager hintManager = HintManager.getInstance()) {
                hintManager.addTableShardingValue(logicTable, businessTimeDTO);
                // 查询预付费收入账单详情分页信息
                return TenantUtils.executeIgnore(callable);
            }
        } else {
            return TenantUtils.executeIgnore(callable);
        }
    }

    default <M, N> void assign(M t, Function<M, Long> idf, Function<Long, CommonResult<N>> query, V v, BiConsumer<V, N> setConsumer) {
        Long id = idf.apply(t);
        if (id != null) {
            // 根据账单的客户ID查询客户信息
            CommonResult<N> commonResult = query.apply(id);
            // 如果查询成功
            if (commonResult.isSuccess()) {
                // 获取客户信息
                N n = commonResult.getData();
                // 设置客户信息到 AccountPrepaidIncomeBillDetailShowVO 对象中
                setConsumer.accept(v, n);
            }
        }
    }

    default <M, N> void assign(M t, Function<M, N> convert, V v, BiConsumer<V, N> setConsumer) {
        if (t != null) {
            N n = convert.apply(t);
            if (n != null) {
                setConsumer.accept(v, n);
            }
        }
    }


    default <F> void handleRateDetail(List<F> bills,
                                      Function<F, String> mapper,
                                      Function<F, Integer> typeMapper,
                                      Function<F, BigDecimal> taxRateMapper,
                                      BiConsumer<F, FixedRateConfigFeeDTO> fixedConsumer,
                                      BiConsumer<F, PackageRateConfigFeeDTO> packageConsumer,
                                      BiConsumer<F, TierRateConfigFeeDTO> tierConsumer,
                                      BiConsumer<F, UsageBasedRateConfigFeeDTO> usageConsumer) {

        if (CollectionUtils.isNotEmpty(bills)) {
            for (F bill : bills) {
                String rateDetails = mapper.apply(bill);
                if (StringUtils.isNotBlank(rateDetails)) {
                    switch (typeMapper.apply(bill)) {
                        case 0:
                            FixedRateConfigFeeDTO fixedRateConfigFeeDTO = JSONUtil.toBean(rateDetails, FixedRateConfigFeeDTO.class);
                            fixedConsumer.accept(bill, fixedRateConfigFeeDTO);
                            computeFee(taxRateMapper.apply(bill), fixedRateConfigFeeDTO);
                            break;
                        case 2:
                            PackageRateConfigFeeDTO rateConfigFeeDTO = JSONUtil.toBean(rateDetails, PackageRateConfigFeeDTO.class);
                            packageConsumer.accept(bill, rateConfigFeeDTO);
                            computeFee(taxRateMapper.apply(bill), rateConfigFeeDTO);
                            break;
                        case 1:
                            TierRateConfigFeeDTO tierRateConfigFeeDTO = JSONUtil.toBean(rateDetails, TierRateConfigFeeDTO.class);//bill.setTierRateConfig(JSONUtil.toBean(rateDetails, TierRateConfigDTO.class));
                            tierConsumer.accept(bill, tierRateConfigFeeDTO);
                            computeFee(taxRateMapper.apply(bill), tierRateConfigFeeDTO);
                            break;
                        case 3:
                            UsageBasedRateConfigFeeDTO usageBasedRateConfigFeeDTO = JSONUtil.toBean(rateDetails, UsageBasedRateConfigFeeDTO.class);
                            usageConsumer.accept(bill, usageBasedRateConfigFeeDTO);
                            computeFee(taxRateMapper.apply(bill), usageBasedRateConfigFeeDTO);
                            break;
                        default:
                            break;

                    }
                }
            }
        }

    }

    default void filter(PackageRateConfigFeeDTO fee) {
        List<PackageRateConfigFeeDTO.PackageConfigFeeDTO> packages = fee.getPackages();
        for (PackageRateConfigFeeDTO.PackageConfigFeeDTO pack : packages) {
            if (pack == null || pack.getOutPackage() == null) {
                continue;
            }
            PackageRateConfigFeeDTO.OutPackageRateFeeDTO outPackage = pack.getOutPackage();
            filterAndAccept(outPackage, PackageRateConfigFeeDTO.OutPackageRateFeeDTO::getStairRatePricesOutPackage, t -> t.getOriginalPrice() != null, PackageRateConfigFeeDTO.OutPackageRateFeeDTO::setStairRatePricesOutPackage);
        }
    }

    default void filter(UsageBasedRateConfigFeeDTO usage) {
        List<UsageBasedRateConfigFeeDTO.StairRateConfigFeeDTO> stairRatePrices = usage.getStairRatePrices();
        if (CollectionUtils.isNotEmpty(stairRatePrices)) {
            for (UsageBasedRateConfigFeeDTO.StairRateConfigFeeDTO stairRatePrice : stairRatePrices) {
                filterAndAccept(stairRatePrice, UsageBasedRateConfigFeeDTO.StairRateConfigFeeDTO::getTierPrices, t -> t.getOriginalPrice() != null, UsageBasedRateConfigFeeDTO.StairRateConfigFeeDTO::setTierPrices);
            }
        }
    }

    default void filter(TierRateConfigFeeDTO tierRateConfigFee) {
        filterAndAccept(tierRateConfigFee, TierRateConfigFeeDTO::getTierPrices, t -> t.getOriginalPrice() != null, TierRateConfigFeeDTO::setTierPrices);
    }


    default <S, T> void filterAndAccept(S s, Function<S, List<T>> mapper, Predicate<T> predicate, BiConsumer<S, List<T>> biConsumer) {
        List<T> list = mapper.apply(s);
        if (CollectionUtils.isNotEmpty(list)) {
            List<T> newList = list.stream().filter(predicate).toList();
            biConsumer.accept(s, newList);
        }
    }


    //@Override
    default void computeFee(BigDecimal taxRate, FixedRateConfigFeeDTO fee) {
        fee.setTaxRate(taxRate);
        fee.setTaxAmount(multi(fee.getDiscountedPrice(), taxRate));
        fee.setTotalAmount(add(fee.getDiscountedPrice(), fee.getTaxAmount()));
    }

    //@Override
    default void computeFee(BigDecimal taxRate, PackageRateConfigFeeDTO fee) {
        filter(fee);
        List<PackageRateConfigFeeDTO.PackageConfigFeeDTO> packages = fee.getPackages();
        if (CollectionUtils.isNotEmpty(packages)) {
            for (PackageRateConfigFeeDTO.PackageConfigFeeDTO pack : packages) {
                PackageRateConfigFeeDTO.InPackageRateFeeDTO inPackage = pack.getInPackage();
                if (inPackage != null) {
                    inPackage.setTaxRate(taxRate);
                    inPackage.setTaxAmount(multi(inPackage.getDiscountedPrice(), taxRate));
                    inPackage.setTotalAmount(add(inPackage.getDiscountedPrice(), inPackage.getTaxAmount()));
                }
                PackageRateConfigFeeDTO.OutPackageRateFeeDTO outPackage = pack.getOutPackage();
                if (outPackage != null) {
                    PackageRateConfigFeeDTO.FixedOutPackageRateFeeDTO fixRatePricesOutPackage = outPackage.getFixRatePricesOutPackage();
                    if (fixRatePricesOutPackage != null) {
                        fixRatePricesOutPackage.setTaxRate(taxRate);
                        fixRatePricesOutPackage.setTaxAmount(multi(fixRatePricesOutPackage.getDiscountedPrice(), taxRate));
                        fixRatePricesOutPackage.setTotalAmount(add(fixRatePricesOutPackage.getDiscountedPrice(), fixRatePricesOutPackage.getTaxAmount()));
                    }
                    List<PackageRateConfigFeeDTO.StairOutPackageRateFeeDTO> stairRatePricesOutPackage = outPackage.getStairRatePricesOutPackage();
                    if (CollectionUtils.isNotEmpty(stairRatePricesOutPackage)) {
                        for (PackageRateConfigFeeDTO.StairOutPackageRateFeeDTO stairOutPackageRateFeeDTO : stairRatePricesOutPackage) {
                            stairOutPackageRateFeeDTO.setTaxRate(taxRate);
                            stairOutPackageRateFeeDTO.setTaxAmount(multi(stairOutPackageRateFeeDTO.getDiscountedPrice(), taxRate));
                            stairOutPackageRateFeeDTO.setTotalAmount(add(stairOutPackageRateFeeDTO.getDiscountedPrice(), stairOutPackageRateFeeDTO.getTaxAmount()));
                        }
                    }
                }
            }
        }
    }


    default void computeFee(BigDecimal taxRate, TierRateConfigFeeDTO fee) {
        filter(fee);
        List<TierRateConfigFeeDTO.TierPriceFeeDTO> tierPrices = fee.getTierPrices();
        if (CollectionUtils.isNotEmpty(tierPrices)) {
            for (TierRateConfigFeeDTO.TierPriceFeeDTO tierPrice : tierPrices) {
                tierPrice.setTaxRate(taxRate);
                tierPrice.setTaxAmount(multi(tierPrice.getDiscountedPrice(), taxRate));
                tierPrice.setTotalAmount(add(tierPrice.getDiscountedPrice(), tierPrice.getTaxAmount()));
            }
        }
    }


    default void computeFee(BigDecimal taxRate, UsageBasedRateConfigFeeDTO fee) {
        filter(fee);
        List<FixedRateConfigFeeDTO> fixRatePrices = fee.getFixRatePrices();
        if (CollectionUtils.isNotEmpty(fixRatePrices)) {
            for (FixedRateConfigFeeDTO fixRatePrice : fixRatePrices) {
                fixRatePrice.setTaxRate(taxRate);
                fixRatePrice.setTaxAmount(multi(fixRatePrice.getDiscountedPrice(), taxRate));
                fixRatePrice.setTotalAmount(add(fixRatePrice.getDiscountedPrice(), fixRatePrice.getTaxAmount()));
            }
        }
        List<UsageBasedRateConfigFeeDTO.StairRateConfigFeeDTO> stairRatePrices = fee.getStairRatePrices();
        if (CollectionUtils.isNotEmpty(stairRatePrices)) {

            for (UsageBasedRateConfigFeeDTO.StairRateConfigFeeDTO stairRatePrice : stairRatePrices) {
                List<TierRateConfigFeeDTO.TierPriceFeeDTO> tierPrices = stairRatePrice.getTierPrices();
                for (TierRateConfigFeeDTO.TierPriceFeeDTO tierPrice : tierPrices) {
                    tierPrice.setTaxRate(taxRate);
                    tierPrice.setTaxAmount(multi(tierPrice.getDiscountedPrice(), taxRate));
                    tierPrice.setTotalAmount(add(tierPrice.getDiscountedPrice(), tierPrice.getTaxAmount()));
                }
            }
        }
    }


    default <G> void handleDiscount(G showVO, Function<G, String> mapper, BiConsumer<G, List<BillDiscountConfigDTO>> consumer) {
        String discountDetails = mapper.apply(showVO);
        if (cn.idev.excel.util.StringUtils.isNotBlank(discountDetails)) {
            consumer.accept(showVO, JSONUtil.toList(discountDetails, BillDiscountConfigDTO.class));
        }
    }

    default <G> void handleDiscount(List<G> showVOs, Function<G, String> mapper, BiConsumer<G, List<BillDiscountConfigDTO>> consumer) {
        if (CollectionUtils.isNotEmpty(showVOs)) {
            for (G showVO : showVOs) {
                handleDiscount(showVO, mapper, consumer);
            }
        }
    }

    default void checkInvoiceCustomerAndEntity(BillInvoiceCreateReqDTO createReqDTO, ErrorCode customerErrorCode, ErrorCode entityErrorCode) {
        if (createReqDTO.getCustomer() == null) {
            CommonResult<ChargeCustomerAccountsInfo> customer = getCustomerApi().getAccountInfo(createReqDTO.getCustomerId());
            if (customer.isSuccess() && customer.getData() != null) {
                createReqDTO.setCustomer(InvoiceBillConvert.INSTANCE.convert(customer.getData()));
            } else {
                getLogger().info("开票客户不存在,customerId:{}", createReqDTO.getCustomerId());
                throw ServiceExceptionUtil.exception(customerErrorCode);
            }
        }
        if (createReqDTO.getEntity() == null) {
            CommonResult<EntityDetailsVO> entity = getEntityApi().findById(createReqDTO.getEntityId());
            if (entity.isSuccess() && entity.getData() != null) {
                createReqDTO.setEntity(InvoiceBillConvert.INSTANCE.convert(entity.getData()));
            } else {
                getLogger().info("出账主体不存在,entityId:{}", createReqDTO.getEntityId());
                throw ServiceExceptionUtil.exception(entityErrorCode);
            }
        }

    }

    default BigDecimal multi(BigDecimal a, BigDecimal b) {
        if (a == null) {
            a = BigDecimal.ZERO;
        }
        if (b == null) {
            b = BigDecimal.ZERO;
        }
        return a.multiply(b);
    }

    default BigDecimal add(BigDecimal a, BigDecimal b) {
        if (a == null) {
            a = BigDecimal.ZERO;
        }
        if (b == null) {
            b = BigDecimal.ZERO;
        }
        return a.add(b);
    }

    CustomerApi getCustomerApi();

    EntityApi getEntityApi();

    Logger getLogger();

    /**
     * 转为草稿
     *
     * @param reqDto 请求参数
     */
    void toDraft(BillReqDto reqDto);


    /**
     * 支付账单 结清账单
     *
     * @param reqDto 请求参数
     */
    void payOffBill(BillReqDto reqDto);

    /**
     * 开具发票 - 校验发票信息
     *
     * @param checkReqDTO 校验请求参数
     */
    void checkInvoice(BillInvoiceCheckDTO checkReqDTO);

    /**
     * 开具发票 - 创建发票
     *
     * @param createReqDTO 创建请求参数
     */
    void createInvoice(BillInvoiceCreateReqDTO createReqDTO);

    /**
     * 取消发票 <=> 发票审核不通过 </=>
     * 回退发票的开票金额
     *
     * @param cancelDTO 取消发票请求参数
     */
    void cancelInvoice(BillInvoiceAmountCancelDTO cancelDTO);


    V queryInvoiceBillDetail(InvoiceBillReqDto billReqDto);

    /**
     * 账单历史记录 分页数据
     *
     * @param reqDto
     * @return
     */
    PageResult<IncomeBillHistoryVO<V>> historyBills(BillReqHistoryDto reqDto);

    /**
     * 删除账单
     *
     * @param reqDto 请求参数
     */
    void deleteBill(BillReqDto reqDto);

    boolean updateBillNotSufficientFundsStatus(Long billId, Integer billStatus, JSONObject billJSON) throws Exception;

    boolean updateBillStatus(Long billId, Integer billStatus, Integer walletsStatus, JSONObject billJSON) throws Exception;


    /**
     * 发票退款
     * @param billId 账单ID
     * @param billingTime 账单时间戳
     * @param refundAmount 退款金额
     */
    boolean refundInvoice(Long billId,Long billingTime, BigDecimal refundAmount);
}
