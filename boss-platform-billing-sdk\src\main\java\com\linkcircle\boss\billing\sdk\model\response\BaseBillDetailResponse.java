package com.linkcircle.boss.billing.sdk.model.response;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-07-25 15:00
 * @description 账单明细响应基类
 */
public abstract class BaseBillDetailResponse {

    /**
     * 账单明细id
     */
    @JsonProperty("bill_detail_id")
    private Long billDetailId;

    /**
     * 账户ID
     */
    @JsonProperty("account_id")
    private Long accountId;

    /**
     * 消耗量
     */
    @JsonProperty("usage")
    private BigDecimal usage;

    /**
     * 消耗量单位
     */
    @JsonProperty("usage_unit")
    private String usageUnit;

    /**
     * 接口请求时间戳
     */
    @JsonProperty("timestamp")
    private Long timestamp;

    /**
     * 请求ID
     */
    @JsonProperty("request_id")
    private String requestId;

    /**
     * 业务唯一ID
     */
    @JsonProperty("business_id")
    private String businessId;

    public BaseBillDetailResponse() {
    }

    public Long getBillDetailId() {
        return billDetailId;
    }

    public void setBillDetailId(Long billDetailId) {
        this.billDetailId = billDetailId;
    }

    public Long getAccountId() {
        return accountId;
    }

    public void setAccountId(Long accountId) {
        this.accountId = accountId;
    }

    public BigDecimal getUsage() {
        return usage;
    }

    public void setUsage(BigDecimal usage) {
        this.usage = usage;
    }

    public String getUsageUnit() {
        return usageUnit;
    }

    public void setUsageUnit(String usageUnit) {
        this.usageUnit = usageUnit;
    }

    public Long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getBusinessId() {
        return businessId;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }
}
