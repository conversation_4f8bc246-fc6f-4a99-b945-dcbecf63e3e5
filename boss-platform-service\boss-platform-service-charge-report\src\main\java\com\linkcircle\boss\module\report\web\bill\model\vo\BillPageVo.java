package com.linkcircle.boss.module.report.web.bill.model.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Schema(description = "账单通用 Response DTO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BillPageVo {
    @Schema(description = "账单类型 1:预付费账单 2:后付费账单 3:手工账单")
    private Integer billType;

    /**
     * 产品账单id
     */
    @Schema(description = "账单id")
    private Long billId;


    /**
     * 客户id
     */
    @Schema(description = "客户id")
    private Long customerId;

    @Schema(description = "客户名称")
    private String customerName;

    /**
     * 账户id
     */
    @Schema(description = "账户id")
    private Long accountId;

    @Schema(description = "账户名称")
    private String accountName;

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long productId;
    /**
     * 产品名称
     */
    @Schema(description = "产品名称")
    private String productName;
    /**
     * 主体id
     */
    @Schema(description = "主体id")
    private Long entityId;
    /**
     * 主体名称
     */
    @Schema(description = "主体名称")
    private String entityName;
    /**
     * 合同id
     */
    @Schema(description = "合同id")
    private Long contractId;
    /**
     * 合同名称
     */
    @Schema(description = "合同名称")
    private String contractName;
    /**
     * 钱包id
     */
    @Schema(description = "钱包id")
    private Long walletId;
    /**
     * 钱包名称
     */
    @Schema(description = "钱包名称")
    private String walletName;
    /**
     * 计划id
     */
    @Schema(description = "计划id")
    private Long planId;

    @Schema(description = "计划名称")
    private String planName;


    /**
     * 订阅id
     */
    @Schema(description = "订阅id")
    private Long subscribeId;


    /**
     * 支付方式 0-现金, 1-积分
     */
    @Schema(description = "支付方式 0-现金, 1-积分")
    private Integer paymentMethod;

    /**
     * 计费类型  0-固定费率, 1-阶梯费率, 2-套餐计费, 3-按量计费
     */
    @Schema(description = "计费类型  0-固定费率, 1-阶梯费率, 2-套餐计费, 3-按量计费")
    private Integer billingType;


    /**
     * 账单状态 0-草稿, 1-待支付, 2-已支付, 3-未结清
     */
    @Schema(description = "账单状态 0-草稿, 1-待支付, 2-已支付, 3-未结清")
    private Integer billStatus;

    /**
     * 税率
     */
    @Schema(description = "税率")
    private BigDecimal taxRate;


    /**
     * 已开票金额
     */
    @Schema(description = "已开票金额")
    private BigDecimal invoicedAmount;

    /**
     * 可开票金额(=优惠价-已开票金额)
     */
    @Schema(description = "可开票金额(=优惠价-已开票金额)")
    private BigDecimal availableInvoiceAmount;

    /**
     * 含税总金额
     */
    @Schema(description = "含税总金额")
    private BigDecimal amountWithTax;

    /**
     * 不含税金额
     */
    @Schema(description = "不含税金额")
    private BigDecimal amountWithoutTax;

    /**
     * 货币单位 CNY USD
     */
    @Schema(description = "货币单位 CNY USD")
    private String currency;

    /**
     * 实际支付时间戳
     */
    @Schema(description = "实际支付时间戳")
    private Long paymentTime;

    /**
     * 出账时间戳
     */
    @Schema(description = "出账时间戳")
    private Long billingTime;

    /**
     * 数据创建时间戳
     */
    @Schema(description = "数据创建时间戳")
    private Long createTime;

    /**
     * 是否删除
     */
    @Schema(description = "是否删除")
    private Boolean deleted;


    @Schema(description = "产品名称信息")
    private List<String> productNames;
    @Schema(description = "产品服务名称信息")
    private List<String> serviceNames;


    /**
     * 出账开始时间戳（毫秒）
     */
    @TableField("billing_start_time")
    @Schema(description = "出账开始时间戳（毫秒）")
    private Long billingStartTime;

    /**
     * 出账结束时间戳（毫秒）
     */
    @TableField("billing_end_time")
    @Schema(description = "出账结束时间戳（毫秒）")
    private Long billingEndTime;


    @TableField("bill_no")
    @Schema(description = "账单号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String billNo;

    /**
     * 开票退款金额
     */
    @TableField("refund_invoice_amount")
    @Schema(description = "开票退款金额")
    private BigDecimal refundInvoiceAmount;
}