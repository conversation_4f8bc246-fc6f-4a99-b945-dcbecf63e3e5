package com.linkcircle.boss.module.billing.web.data.model.vo;

import com.linkcircle.boss.module.crm.enums.ChargeRateTypeEnum;
import com.linkcircle.boss.module.crm.enums.PeriodUnitEnum;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025-06-20 17:21
 * @description 周期计算结果
 */
@Data
public class CyclePeriodResultVO {

    /**
     * 订阅开始时间
     */
    private Long startTime;

    /**
     * 订阅结束时间
     */
    private Long endTime;

    /**
     * 出账周期标识（如202507-202509）
     */
    private String billingCycle;

    /**
     * 间隔时长
     */
    private Integer period;

    /**
     * 周期单位枚举
     */
    private PeriodUnitEnum periodUnitEnum;

    /**
     * 计费类型枚举
     */
    private ChargeRateTypeEnum rateTypeEnum;

    /**
     * 资源采购信息id
     */
    private Long resourceId;

    /**
     * 时区
     */
    private String timezone;

    /**
     * 币种
     */
    private String currency;

    /**
     * 订阅ID
     */
    private Long subscriptionId;

    /**
     * 账户ID
     */
    private Long accountId;

    /**
     * 服务ID
     */
    private Long serviceId;

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 周期索引（从0开始）
     */
    private Long cycleIndex;

    /**
     * 当前周期开始时间戳
     */
    private Long cycleStartTime;

    /**
     * 当前周期结束时间戳
     */
    private Long cycleEndTime;

    /**
     * 业务时间戳
     */
    private Long businessTime;

    /**
     * 错误信息
     */
    private String errorMessage;

    public CyclePeriodResultVO() {
    }

    public static CyclePeriodResultVO success(Long cycleIndex, Long cycleStartTime, Long cycleEndTime, Long businessTime) {
        CyclePeriodResultVO result = new CyclePeriodResultVO();
        result.success = true;
        result.cycleIndex = cycleIndex;
        result.cycleStartTime = cycleStartTime;
        result.cycleEndTime = cycleEndTime;
        result.businessTime = businessTime;
        return result;
    }

    public static CyclePeriodResultVO notStarted() {
        CyclePeriodResultVO result = new CyclePeriodResultVO();
        result.success = false;
        result.errorMessage = "业务时间还未到达开始时间";
        return result;
    }

    /**
     * 创建失败结果
     *
     * @param errorMessage 错误信息
     * @return 失败结果
     */
    public static CyclePeriodResultVO failure(String errorMessage) {
        CyclePeriodResultVO result = new CyclePeriodResultVO();
        result.success = false;
        result.errorMessage = errorMessage;
        return result;
    }

    public Boolean isSuccess() {
        return success;
    }

    public Long getCycleIndex() {
        return cycleIndex;
    }

    public Long getCycleStartTime() {
        return cycleStartTime;
    }

    public Long getCycleEndTime() {
        return cycleEndTime;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    /**
     * 获取第几个周期（从1开始）
     */
    public Long getCycleNumber() {
        return cycleIndex + 1;
    }

    @Override
    public String toString() {
        if (!success) {
            return "CyclePeriodResultVO{success=false, errorMessage='" + errorMessage + "'}";
        }
        return "CyclePeriodResultVO{" +
                "cycleIndex=" + cycleIndex +
                ", cycleNumber=" + getCycleNumber() +
                ", cycleStartTime=" + cycleStartTime +
                ", cycleEndTime=" + cycleEndTime +
                '}';
    }
}
