# Boss Platform Billing SDK

计费平台数据推送SDK，支持收入和成本账单明细推送，兼容JDK 8和JDK 21。

## 特性

- 🚀 **简单易用**: 提供链式API和构建器模式，简化开发
- 🔐 **安全可靠**: 自动处理签名验证和参数校验
- 🔄 **自动重试**: 内置重试机制，提高推送成功率
- 📝 **详细日志**: 支持调试模式，便于问题排查
- ⚡ **高性能**: 基于OkHttp，支持连接池和异步处理
- 🛡️ **异常处理**: 完善的异常体系，便于错误处理

## 快速开始

### 1. 添加依赖

```xml
<dependency>
    <groupId>com.linkcircle.boss</groupId>
    <artifactId>boss-platform-billing-sdk</artifactId>
    <version>1.0.0</version>
</dependency>
```

### 2. 创建客户端

```java
import com.linkcircle.boss.billing.sdk.BillingSDK;
import com.linkcircle.boss.billing.sdk.BillingClient;

// 简单创建
BillingClient client = BillingSDK.createClient(
    "http://localhost:49086",  // 计费平台地址
    "your_app_id",             // 应用ID
    "your_app_secret",         // 应用密钥
    1L                         // 租户ID
);
```

### 3. 推送收入账单明细

```java
import com.linkcircle.boss.billing.sdk.model.request.IncomeBillDetailRequest;
import com.linkcircle.boss.billing.sdk.model.response.IncomeBillDetailResponse;

// 构建请求
IncomeBillDetailRequest request = BillingSDK.incomeRequest()
    .accountId(123456L)
    .productId(789L)
    .serviceId(456L)
    .businessTime(System.currentTimeMillis())
    .usage(100.5)
    .usageUnit("minutes")
    .callbackUrl("https://your-domain.com/callback")
    .businessId("voice_call_" + System.currentTimeMillis())
    .addData("call_duration", 100.5)
    .addData("call_type", "voice")
    .addData("from_number", "***********")
    .addData("to_number", "***********")
    .build();

// 推送
IncomeBillDetailResponse response = client.pushIncomeBillDetail(request);
System.out.println("账单ID: " + response.getBillDetailId());
```

### 4. 推送成本账单明细

```java
import com.linkcircle.boss.billing.sdk.model.request.CostBillDetailRequest;
import com.linkcircle.boss.billing.sdk.model.response.CostBillDetailResponse;

// 构建请求
CostBillDetailRequest request = BillingSDK.costRequest()
    .accountId(123456L)
    .resourceId(789L)
    .resourceServiceId(456L)
    .businessTime(System.currentTimeMillis())
    .usage(50.0)
    .usageUnit("GB")
    .callbackUrl("https://your-domain.com/callback")
    .businessId("bandwidth_" + System.currentTimeMillis())
    .addData("bandwidth", 50.0)
    .addData("region", "cn-beijing")
    .build();

// 推送
CostBillDetailResponse response = client.pushCostBillDetail(request);
System.out.println("账单ID: " + response.getBillDetailId());
```

## 高级配置

```java
import com.linkcircle.boss.billing.sdk.config.BillingConfig;

BillingConfig config = BillingConfig.builder()
    .baseUrl("http://localhost:49086")
    .appId("your_app_id")
    .appSecret("your_app_secret")
    .tenantId(1L)
    .connectTimeout(30000)    // 连接超时30秒
    .readTimeout(60000)       // 读取超时60秒
    .writeTimeout(60000)      // 写入超时60秒
    .maxRetries(3)            // 最大重试3次
    .debug(true)              // 开启调试模式
    .build();

BillingClient client = BillingSDK.createClient(config);
```

## 错误处理

```java
import com.linkcircle.boss.billing.sdk.exception.BillingException;

try {
    IncomeBillDetailResponse response = client.pushIncomeBillDetail(request);
    // 处理成功响应
} catch (BillingException e) {
    // 根据错误码进行不同处理
    switch (e.getErrorCode()) {
        case "PARAM_ERROR":
            System.err.println("参数错误: " + e.getMessage());
            break;
        case "HTTP_ERROR":
            System.err.println("网络错误: " + e.getMessage());
            break;
        case "API_ERROR":
            System.err.println("API错误: " + e.getMessage());
            break;
        default:
            System.err.println("未知错误: " + e.getMessage());
            break;
    }
}
```

## API接口

### 收入账单明细接口
- **URL**: `/billing/api/v3/income/charge-income-bill-detail`
- **方法**: POST
- **说明**: 推送收入相关的消费数据

### 成本账单明细接口
- **URL**: `/billing/api/v3/cost/charge-cost-bill-detail`
- **方法**: POST
- **说明**: 推送成本相关的消费数据

## 参数说明

### 公共参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| account_id | Long | 是 | 账户ID |
| business_time | Long | 是 | 业务产生的时间戳(毫秒) |
| usage | BigDecimal | 是 | 消耗量 |
| usage_unit | String | 是 | 消耗量单位 |
| callback_url | String | 是 | 扣费回调URL |
| business_id | String | 是 | 业务唯一ID |
| data | Map | 是 | 业务话单数据 |

### 收入账单特有参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| product_id | Long | 是 | 产品ID |
| service_id | Long | 是 | 服务ID |

### 成本账单特有参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| resource_id | Long | 是 | 资源ID |
| resource_service_id | Long | 是 | 资源服务ID |

## 注意事项

1. **签名验证**: SDK会自动处理签名生成和验证，无需手动处理
2. **请求ID**: 如果不提供requestId，SDK会自动生成唯一ID
3. **时间戳**: 如果不提供timestamp，SDK会自动使用当前时间
4. **重试机制**: 网络异常时会自动重试，可通过配置调整重试次数
5. **资源释放**: 使用完毕后请调用`client.close()`释放资源

## 依赖说明

- **Hutool**: 提供工具类支持
- **Jackson**: JSON序列化/反序列化
- **OkHttp**: HTTP客户端
- **SLF4J**: 日志接口

## 兼容性

- **JDK版本**: 支持JDK 8及以上版本
- **Spring Boot**: 无依赖，可在任何Java项目中使用

## 示例代码

完整的示例代码请参考：
- [QuickStartExample.java](src/main/java/com/linkcircle/boss/billing/sdk/example/QuickStartExample.java)
- [BillingSDKTest.java](src/test/java/com/linkcircle/boss/billing/sdk/BillingSDKTest.java)

## 许可证

本项目采用 MIT 许可证。
