package com.linkcircle.boss.module.charge.fee.web.bill.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.linkcircle.boss.framework.common.exception.ServiceException;
import com.linkcircle.boss.framework.common.exception.util.ServiceExceptionUtil;
import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.framework.sharding.algorithms.dto.HitBusinessTimeDTO;
import com.linkcircle.boss.framework.tanant.TenantUtils;
import com.linkcircle.boss.framework.web.context.LoginUser;
import com.linkcircle.boss.framework.web.core.util.WebFrameworkUtils;
import com.linkcircle.boss.module.billing.api.bill.product.model.entity.PostpaidProductIncomeBillDO;
import com.linkcircle.boss.module.billing.api.bill.product.model.entity.PostpaidProductServiceIncomeBillDO;
import com.linkcircle.boss.module.charge.fee.constants.ErrorCodeConstants;
import com.linkcircle.boss.module.charge.fee.enums.BillEnum;
import com.linkcircle.boss.module.charge.fee.enums.InvoiceEnum;
import com.linkcircle.boss.module.charge.fee.web.bill.mapper.*;
import com.linkcircle.boss.module.charge.fee.web.bill.model.dto.BillReqDto;
import com.linkcircle.boss.module.charge.fee.web.bill.model.dto.calculate.BillFeeCalculateReqDTO;
import com.linkcircle.boss.module.charge.fee.web.bill.model.dto.calculate.BillProductFeeCalculateReqDTO;
import com.linkcircle.boss.module.charge.fee.web.bill.model.entity.IncomeBillHistoryDO;
import com.linkcircle.boss.module.charge.fee.web.bill.model.entity.MakeupIncomeBillDO;
import com.linkcircle.boss.module.charge.fee.web.bill.model.entity.MakeupProductIncomeBillDO;
import com.linkcircle.boss.module.charge.fee.web.bill.model.entity.MakeupProductServiceIncomeBillDO;
import com.linkcircle.boss.module.charge.fee.web.bill.model.vo.calculate.BillFeeResponseVO;
import com.linkcircle.boss.module.charge.fee.web.bill.model.vo.calculate.BillProductFeeResponseVO;
import com.linkcircle.boss.module.charge.fee.web.bill.model.vo.calculate.MakeupBillVO;
import com.linkcircle.boss.module.charge.fee.web.bill.model.vo.calculate.PostpaidProductBillVO;
import com.linkcircle.boss.module.charge.fee.web.bill.service.*;
import com.linkcircle.boss.module.charge.fee.web.bill.service.impl.fee.makeup.MakeupBillHandler;
import com.linkcircle.boss.module.charge.fee.web.bill.service.impl.fee.makeup.MakeupProductBillHandler;
import com.linkcircle.boss.module.charge.fee.web.bill.service.impl.fee.makeup.MakeupProductServiceBillHandler;
import com.linkcircle.boss.module.charge.fee.web.bill.service.impl.fee.postpaid.PostPaidProductBillHandler;
import com.linkcircle.boss.module.charge.fee.web.bill.service.impl.fee.postpaid.PostPaidProductServiceBillHandler;
import com.linkcircle.boss.module.charge.fee.web.bill.util.NumberUtil;
import com.linkcircle.boss.module.charge.fee.web.payment.mapper.PostpaidProductServiceIncomeBillMapper;
import com.linkcircle.boss.module.crm.api.customer.account.CustomerAccountApi;
import com.linkcircle.boss.module.crm.api.customer.account.vo.CustomerAccountVO;
import com.linkcircle.boss.module.crm.api.customer.customer.CustomerApi;
import com.linkcircle.boss.module.crm.api.entity.EntityApi;
import com.linkcircle.boss.module.crm.api.productservice.ChargeProductServiceApi;
import com.linkcircle.boss.module.crm.api.productservice.vo.ProductServiceVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.apache.shardingsphere.infra.hint.HintManager;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.Callable;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/7/15 14:12
 * <p>
 * @description 账单费用服务实现类，用于计算和生成账单费用。
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class BillFeeServiceImpl implements BillFeeService {


    private final CustomerAccountApi customerAccountApi;


    private final MakeupBillHandler makeupBillHandler;
    private final MakeupProductServiceBillHandler makeupProductServiceBillHandler;
    private final MakeupProductBillHandler makeupProductBillHandler;
    private final PostPaidProductBillHandler postPaidProductBillHandler;
    private final PostPaidProductServiceBillHandler postPaidProductServiceBillHandler;

    private final BillFeeCalculateService billFeeCalculateService;


    private final PostpaidMapper postpaidMapper;
    private final PostpaidProductServiceIncomeBillMapper postpaidProductServiceIncomeBillMapper;
    private final MakeupBillMapper makeupBillMapper;
    private final MakeupProductServiceBillMapper makeupProductServiceBillMapper;
    private final MakeupProductBillMapper makeupProductBillMapper;

    private final IncomeBillHistoryMapper incomeBillHistoryMapper;

    private final MakeupBillService makeupBillService;

    private final PostpaidService postpaidService;


    private final PrepaidMapper prepaidMapper;


    private final ChargeProductServiceApi chargeProductServiceApi;

    public List<String> listServiceCodes() {
        CommonResult<List<ProductServiceVO>> listCommonResult = chargeProductServiceApi.allService();
        if (listCommonResult.isSuccess()) {
            return listCommonResult.getData().stream().map(ProductServiceVO::getServiceCode).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        }
        return List.of();
    }
    /**
     * 根据账户ID获取客户账户信息
     *
     * @param accountId 客户账户ID
     * @return 客户账户信息对象
     * @throws ServiceException 如果客户账户不存在，则抛出ServiceException异常
     */
    private CustomerAccountVO getCustomerAccountVO(Long accountId) {
        // 调用客户账户API获取客户账户信息
        CommonResult<CustomerAccountVO> customerAccount = customerAccountApi.getCustomerAccountInfo(accountId);
        if (customerAccount.isSuccess()) {
            // 如果获取成功，返回客户账户信息对象
            return customerAccount.getData();
        } else {
            // 如果获取失败，记录错误日志
            log.error("客户账户不存在:{}", accountId);
            // 抛出客户账户不存在的ServiceException异常
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.CUSTOMER_ACCOUNT_NOT_FOUND, accountId);
        }
    }


    /**
     * 计算产品服务费用
     *
     * @param productFeeReq 产品费用计算请求对象
     * @return 产品费用响应对象
     * @throws ServiceException 当订阅ID为空时抛出此异常
     */
    @Override
    public BillProductFeeResponseVO computeProductServiceFee(BillProductFeeCalculateReqDTO productFeeReq) {
        // 判断账单类型是否为后付费
        if (Objects.equals(productFeeReq.getBillType(), InvoiceEnum.BillType.POSTPAID.getCode())) {
            // 判断订阅ID是否为空
            if (productFeeReq.getSubscribeId() == null) {
                // 打印错误日志
                log.error("订阅id为空");
                // 抛出异常
                throw ServiceExceptionUtil.exception(ErrorCodeConstants.COMPUTE_PRODUCT_SERVICE_FEE_REQ_SUBSCRIPTION_ID_EMPTY);
            }
        }
        // 计算产品服务费用
        return billFeeCalculateService.computeProductServiceFee(productFeeReq);

    }


    /**
     * 计算账单费用
     *
     * @param calculateReq 账单费用计算请求参数
     * @return BillFeeResponseVO 账单费用响应对象
     * @throws ServiceException 自定义异常，用于抛出服务异常
     */
    @Override
    public BillFeeResponseVO computeBillFee(BillFeeCalculateReqDTO calculateReq) {
        // 判断创建账单信息是否为空且更新账单信息不为空
        if (calculateReq.getCreateBillInfo() == null && calculateReq.getUpdateBillInfo() != null) {
            log.info("账单计算失败 账单信息为空");
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.COMPUTE_BILL_FEE_REQ_BILL_INFO_EMPTY);
        }
        // 判断创建账单信息和更新账单信息是否都不为空
        if (calculateReq.getCreateBillInfo() != null && calculateReq.getUpdateBillInfo() != null) {
            log.info("账单计算失败 账单信息异常");
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.COMPUTE_BILL_FEE_REQ_BILL_INFO_ERROR);
        }

        // 创建账单时查询客户账户信息
        // 创建账单信息不为空
        if (calculateReq.getCreateBillInfo() != null) {
            CustomerAccountVO customerAccountVO = getCustomerAccountVO(calculateReq.getCreateBillInfo().getAccountId());
            calculateReq.setExtInfo(customerAccountVO);
            MakeupBillVO makeupBill = makeupBillHandler.handle(calculateReq, makeupProductBillHandler, makeupProductServiceBillHandler);
            return makeupBill.getBillFeeResponse();
        } else {
            BillFeeCalculateReqDTO.UpdateBillInfo updateBillInfo = calculateReq.getUpdateBillInfo();
            // 判断更新账单类型为后付费
            if (Objects.equals(updateBillInfo.getBillType(), InvoiceEnum.BillType.POSTPAID.getCode())) {
                PostpaidProductBillVO productBill = postPaidProductBillHandler.handle(calculateReq, postPaidProductServiceBillHandler);
                return productBill.getBillFeeResponse();
            }
            // 处理其他类型的更新账单
            MakeupBillVO makeupBill = makeupBillHandler.handle(calculateReq, makeupProductBillHandler, makeupProductServiceBillHandler);
            return makeupBill.getBillFeeResponse();
        }

    }


    /**
     * 创建账单 - 手工账单
     *
     * @param calculateReq 账单计算请求对象，包含创建账单所需的信息
     * @return BillFeeResponseVO 账单费用响应对象，包含账单的创建结果和相关信息
     * @throws ServiceException 如果创建账单信息为空，则抛出该异常
     */
    @Override
    public BillFeeResponseVO createBill(BillFeeCalculateReqDTO calculateReq) {
        // 检查创建账单信息是否为空
        if (calculateReq.getCreateBillInfo() == null) {
            log.info("创建手工账单时，CreateBillInfo不能为空");
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.CREATE_BILL_INFO_IS_NULL);
        }

        // 获取客户账户信息
        CustomerAccountVO customerAccountVO = getCustomerAccountVO(calculateReq.getCreateBillInfo().getAccountId());
        calculateReq.setExtInfo(customerAccountVO);

        // 处理账单信息
        MakeupBillVO makeupBill = makeupBillHandler.handle(calculateReq, makeupProductBillHandler, makeupProductServiceBillHandler);

        // 获取账单对象
        MakeupIncomeBillDO bill = makeupBill.getBill();
        // 插入账单信息到数据库
        TenantUtils.executeIgnore(() -> makeupBillMapper.insert(bill));

        // 获取账单产品列表
        List<MakeupProductIncomeBillDO> products = makeupBill.getProducts();
        // 插入账单产品信息到数据库
        TenantUtils.executeIgnore(() -> makeupProductBillMapper.insert(products));

        // 获取账单服务列表
        List<MakeupProductServiceIncomeBillDO> services = makeupBill.getServices();
        // 插入账单服务信息到数据库
        TenantUtils.executeIgnore(() -> makeupProductServiceBillMapper.insert(services));

        // 返回账单费用响应对象
        return makeupBill.getBillFeeResponse();
    }


    /**
     * 确认账单的方法
     *
     * @param calculateReq 账单计算请求对象
     * @return 账单费用响应对象
     * @throws ServiceException 当UpdateBillInfo为空时抛出此异常
     */
    @Override
    public BillFeeResponseVO makeSureBill(BillFeeCalculateReqDTO calculateReq) {
        // 检查UpdateBillInfo是否为空
        if (calculateReq.getUpdateBillInfo() == null) {
            // 如果为空，记录日志并抛出异常
            log.info("确认账单失败，UpdateBillInfo不能为空");
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.UPDATE_BILL_INFO_IS_NULL);
        }
        // 获取UpdateBillInfo对象
        BillFeeCalculateReqDTO.UpdateBillInfo updateBillInfo = calculateReq.getUpdateBillInfo();
        // 判断账单类型是否为后付费
        if (Objects.equals(updateBillInfo.getBillType(), InvoiceEnum.BillType.POSTPAID.getCode())) {
            // 处理后付费账单
            PostpaidProductBillVO postpaidProductBill = postPaidProductBillHandler.handle(calculateReq, postPaidProductServiceBillHandler);
            // 获取产品账单信息
            PostpaidProductIncomeBillDO product = postpaidProductBill.getProduct();
            // 更新产品账单信息
            TenantUtils.executeIgnore(() -> postpaidMapper.updateByIdAndTime(product));
            // 获取服务账单信息列表
            List<PostpaidProductServiceIncomeBillDO> services = postpaidProductBill.getServices();
            // 更新服务账单信息列表
            TenantUtils.executeIgnore(() -> postpaidProductServiceIncomeBillMapper.updateByIdAndTime(services));
            // 记录历史账单
            markHistory(postpaidProductBill.getProduct().getProductBillId(), postpaidProductBill.getProduct().getBillingTime(), postpaidProductBill.getProduct().getAmountWithTax(), InvoiceEnum.BillType.POSTPAID.getCode());
            // 返回账单费用响应对象
            return postpaidProductBill.getBillFeeResponse();
        } else {
            // 处理补单账单
            MakeupBillVO makeupBill = makeupBillHandler.handle(calculateReq, makeupProductBillHandler, makeupProductServiceBillHandler);
            // 获取账单信息
            MakeupIncomeBillDO bill = makeupBill.getBill();
            // 更新账单信息
            TenantUtils.executeIgnore(() -> makeupBillMapper.updateByIdAndTime(bill));
            // 获取产品信息列表
            List<MakeupProductIncomeBillDO> products = makeupBill.getProducts();
            // 更新产品信息列表
            TenantUtils.executeIgnore(() -> makeupProductBillMapper.updateByIdAndTime(products));
            // 获取服务信息列表
            List<MakeupProductServiceIncomeBillDO> services = makeupBill.getServices();
            // 更新服务信息列表
            TenantUtils.executeIgnore(() -> makeupProductServiceBillMapper.updateByIdAndTime(services));
            // 记录历史账单
            markHistory(bill.getBillId(), updateBillInfo.getBillingTime(), bill.getAmountWithTax(), InvoiceEnum.BillType.MAKEUP.getCode());
            // 返回账单费用响应对象
            return makeupBill.getBillFeeResponse();
        }

    }

    public  <R> R executeIgnore(Long startTime,Long endTime, List<String> serviceCodes, String logicTable, Callable<R> callable) {
        // 指定时间范围 只能有两个元素
        HitBusinessTimeDTO businessTimeDTO = HitBusinessTimeDTO.build(List.of(
                startTime, endTime
        ), serviceCodes);
        try (HintManager hintManager = HintManager.getInstance()) {
            hintManager.addTableShardingValue(logicTable, businessTimeDTO);
            // 查询预付费收入账单详情分页信息
            return TenantUtils.executeIgnore(callable);
        }
    }

    @Override
    public Long computeCustomerUnPaidBillNum(Long customerId) {
        long prepaidNum = executeIgnore(0L,System.currentTimeMillis(),listServiceCodes(), BillEnum.Table.PRE_PAID_INCOME_BILL_DETAIL.getCode(),()->prepaidMapper.countUnPaidBill(customerId));
        long postpaidNum =  TenantUtils.executeIgnore(()->postpaidMapper.countUnPaidBill(customerId));
        long makeupNum =  TenantUtils.executeIgnore(()->makeupBillMapper.countUnPaidBill(customerId));
        return prepaidNum + postpaidNum + makeupNum;
    }

    @Override
    public BigDecimal computeConstructFee(Long constructId, Long startTime, Long endTime) {
        BigDecimal prepaidFee = executeIgnore(startTime==null?0L:startTime,endTime==null?System.currentTimeMillis():endTime,listServiceCodes(), BillEnum.Table.PRE_PAID_INCOME_BILL_DETAIL.getCode(),()->prepaidMapper.calculateBillFee(constructId,startTime,endTime));
        BigDecimal postpaidFEE = TenantUtils.executeIgnore(()->postpaidMapper.calculateBillFee(constructId,startTime,endTime));
        BigDecimal makeupFee = TenantUtils.executeIgnore(()->makeupBillMapper.calculateBillFee(constructId,startTime,endTime));
        return prepaidFee.add(postpaidFEE).add(makeupFee);
    }


    /**
     * 标记账单历史记录
     *
     * @param billId      账单ID
     * @param billingTime 账单时间
     * @param billAmount  账单金额
     * @param billType    账单类型
     */
    private void markHistory(Long billId, Long billingTime, BigDecimal billAmount, Integer billType) {
        // 初始化detailVO对象为null
        Object detailVO = null;

        // 判断账单类型是否为MAKEUP
        if (InvoiceEnum.BillType.MAKEUP.getCode().equals(billType)) {
            // 如果是MAKEUP类型的账单，调用makeupBillService的queryBill方法查询账单详情
            detailVO = makeupBillService.queryBill(BillReqDto.builder().billId(billId).billingTime(billingTime).build(), true);
        } else {
            // 如果不是MAKEUP类型的账单，调用postpaidService的queryBill方法查询账单详情
            detailVO = postpaidService.queryBill(BillReqDto.builder().billId(billId).billingTime(billingTime).build(), true);
        }

        // 调用TenantUtils的executeIgnore方法，查询上一次账单历史记录
        IncomeBillHistoryDO last = TenantUtils.executeIgnore(() -> incomeBillHistoryMapper.lastBillingHistory(billId, billingTime, true));

        // 获取当前登录用户
        LoginUser user = WebFrameworkUtils.getLoginUser();

        // 创建IncomeBillHistoryDO对象，并设置相关属性
        IncomeBillHistoryDO historyDO = IncomeBillHistoryDO.builder()
                .historyId(IdUtil.getSnowflakeNextId()) // 设置历史记录ID
                .billId(billId) // 设置账单ID
                .billingTime(billingTime) // 设置账单时间
                .billType(billType) // 设置账单类型
                .billAmount(billAmount) // 设置账单金额
                .creator(user == null ? "default" : user.getUsername()) // 设置创建者用户名
                .createTime(System.currentTimeMillis()) // 设置创建时间
                .deleted(last == null ? 1 : 0) // 设置删除标记，如果last为null则设置为1，否则设置为0
                .billDetail(JSONUtil.toJsonStr(detailVO)) // 设置账单详情
                .build();

        // 调用TenantUtils的executeIgnore方法，将历史记录插入到数据库中
        TenantUtils.executeIgnore(() -> incomeBillHistoryMapper.insert(historyDO));
    }

}
