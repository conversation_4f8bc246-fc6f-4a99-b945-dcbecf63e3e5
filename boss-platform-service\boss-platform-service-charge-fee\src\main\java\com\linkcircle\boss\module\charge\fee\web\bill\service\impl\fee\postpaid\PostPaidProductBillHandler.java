package com.linkcircle.boss.module.charge.fee.web.bill.service.impl.fee.postpaid;

import com.linkcircle.boss.framework.common.exception.util.ServiceExceptionUtil;
import com.linkcircle.boss.framework.tanant.TenantUtils;
import com.linkcircle.boss.module.billing.api.bill.product.model.entity.PostpaidProductIncomeBillDO;
import com.linkcircle.boss.module.billing.api.bill.product.model.entity.PostpaidProductServiceIncomeBillDO;
import com.linkcircle.boss.module.charge.fee.constants.ErrorCodeConstants;
import com.linkcircle.boss.module.charge.fee.enums.InvoiceEnum;
import com.linkcircle.boss.module.charge.fee.web.bill.chain.InterceptorHandler;
import com.linkcircle.boss.module.charge.fee.web.bill.mapper.PostpaidMapper;
import com.linkcircle.boss.module.charge.fee.web.bill.model.dto.calculate.*;
import com.linkcircle.boss.module.charge.fee.web.bill.model.vo.calculate.BillFeeResponseVO;
import com.linkcircle.boss.module.charge.fee.web.bill.model.vo.calculate.BillProductFeeResponseVO;
import com.linkcircle.boss.module.charge.fee.web.bill.model.vo.calculate.PostpaidProductBillVO;
import com.linkcircle.boss.module.charge.fee.web.bill.service.BillFeeCalculateService;
import com.linkcircle.boss.module.charge.fee.web.bill.service.impl.fee.common.JsonApi;
import com.linkcircle.boss.module.charge.fee.web.bill.service.impl.fee.fixed.FixedCalculatePriceHandler;
import com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.Coupon;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/17 17:20
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PostPaidProductBillHandler extends InterceptorHandler<BillFeeCalculateReqDTO, PostpaidProductBillVO> implements JsonApi {


    private final PostpaidMapper postpaidMapper;
    private final BillFeeCalculateService billFeeCalculateService;
    private final FixedCalculatePriceHandler fixedCalculatePriceHandler;

    @Override
    protected PostpaidProductBillVO doPreHandle(BillFeeCalculateReqDTO request, PostpaidProductBillVO productBill1) {
        PostpaidProductBillVO billVO = new PostpaidProductBillVO();
        billVO.setBillType(InvoiceEnum.BillType.POSTPAID.getCode());
        billVO.setUpdate(true);
        // 判读 哪些是更新 哪些是删除 哪些是新增
        List<PostpaidProductIncomeBillDO> productIncomeBills = TenantUtils.executeIgnore(()->postpaidMapper.queryBillByIds(List.of(request.getUpdateBillInfo().getBillId()), request.getUpdateBillInfo().getBillingTime(), request.getUpdateBillInfo().getBillingTime()));

        if (CollectionUtils.isEmpty(productIncomeBills)) {
            log.info("没有查询到账单信息,账单id:{}", request.getUpdateBillInfo().getBillId());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.POSTPAID_INCOME_BILL_NOT_EXIST);
        }
        PostpaidProductIncomeBillDO oldBill = productIncomeBills.getFirst();

        List<BillProductFeeResponseVO> fees = new ArrayList<>();
        for (BillProductFeeReqDTO productServiceFeeReq : request.getProductFees()) {
            BillProductFeeCalculateReqDTO productFeeReq = convertToReq(productServiceFeeReq, oldBill);
            BillProductFeeResponseVO feeResponseVO = billFeeCalculateService.computeProductServiceFee(productFeeReq);
            fees.add(feeResponseVO);
            feeResponseVO.setProductBillId(productServiceFeeReq.getProductBillId());
        }
        BillFeeResponseVO billFeeResponse = new BillFeeResponseVO();
        billFeeResponse.setProductResponseFees(fees);
        billFeeResponse.setBillType(billVO.getBillType());
        billVO.setBillFeeResponse(billFeeResponse);


        oldBill.setEntityId(request.getUpdateBillInfo().getEntityId());
        oldBill.setOriginalPrice(BigDecimal.ZERO);
        oldBill.setDiscountedPrice(BigDecimal.ZERO);
        oldBill.setDiscountAmount(BigDecimal.ZERO);
        oldBill.setBillDiscountAmount(BigDecimal.ZERO);
        oldBill.setInvoicedAmount(BigDecimal.ZERO);
        oldBill.setAvailableInvoiceAmount(BigDecimal.ZERO);
        oldBill.setAmountWithTax(BigDecimal.ZERO);
        oldBill.setAmountWithoutTax(BigDecimal.ZERO);
        oldBill.setPaidAmount(BigDecimal.ZERO);
        oldBill.setUnpaidAmount(BigDecimal.ZERO);
        oldBill.setTaxRate(BigDecimal.ZERO);
        oldBill.setBillDiscountDetails(toStr(request.getCouponList()));
        oldBill.setRefundInvoiceAmount(BigDecimal.ZERO);
        billVO.setProduct(oldBill);
        return billVO;
    }


    private BillProductFeeCalculateReqDTO convertToReq(BillProductFeeReqDTO productServiceFeeReq, PostpaidProductIncomeBillDO oldBill) {
        BillProductFeeCalculateReqDTO billProductFee = new BillProductFeeCalculateReqDTO();
        billProductFee.setBillType(InvoiceEnum.BillType.MAKEUP.getCode());
        billProductFee.setProductId(productServiceFeeReq.getProductId());
        billProductFee.setBillingStartTime(oldBill.getBillingTime());
        billProductFee.setBillingEndTime(oldBill.getBillingTime());
        billProductFee.setCustomerId(oldBill.getCustomerId());
        billProductFee.setAccountId(oldBill.getAccountId());
        billProductFee.setSubscribeId(oldBill.getSubscribeId());
        List<BillProductServiceFeeCalculateReqDTO> serviceFees = new ArrayList<>();
        for (BillProductServiceFeeReq productServiceFee : productServiceFeeReq.getProductServiceFees()) {
            BillProductServiceFeeCalculateReqDTO service = new BillProductServiceFeeCalculateReqDTO();
            service.setProductId(productServiceFee.getProductId());
            service.setServiceId(productServiceFee.getServiceId());
            service.setUsageCount(productServiceFee.getUsageCount());
            service.setTaxRate(productServiceFee.getTaxRate());
            service.setProductServiceBillId(productServiceFee.getProductServiceBillId());
            service.setProductBillId(productServiceFeeReq.getProductBillId());
            serviceFees.add(service);
        }
        billProductFee.setProductServiceFees(serviceFees);
        return billProductFee;
    }

    @Override
    protected PostpaidProductBillVO doPostHandle(BillFeeCalculateReqDTO request, PostpaidProductBillVO productBill) {

        PostpaidProductIncomeBillDO product = productBill.getProduct();
        List<PostpaidProductServiceIncomeBillDO> productServices = productBill.getServices();
        BigDecimal productAmountWithoutTax = BigDecimal.ZERO;
        BigDecimal subTotalAmount = BigDecimal.ZERO;
        for (PostpaidProductServiceIncomeBillDO productService : productServices) {
            productAmountWithoutTax = productAmountWithoutTax.add(productService.getAmountWithoutTax());
            subTotalAmount = subTotalAmount.add(productService.getAmountWithTax());
        }
        product.setOriginalPrice(productAmountWithoutTax);
        List<Coupon> productCoupons = toList(product.getBillDiscountDetails(), Coupon.class);
        BigDecimal discountAmount = fixedCalculatePriceHandler.calculateDiscountPrice(product.getOriginalPrice(), productCoupons);
        product.setDiscountAmount(discountAmount);
        product.setAmountWithoutTax(product.getOriginalPrice().subtract(product.getDiscountAmount()));
        //product.setTaxAmount(product.getTaxRate().multiply(product.getAmountWithoutTax()));
        BigDecimal taxAmount = product.getTaxRate().multiply(product.getAmountWithoutTax());
        product.setAmountWithTax(product.getAmountWithoutTax().add(taxAmount));

        product.setAvailableInvoiceAmount(product.getAmountWithoutTax());
        product.setInvoicedAmount(BigDecimal.ZERO);


        BillFeeResponseVO billFeeResponse = productBill.getBillFeeResponse();
        billFeeResponse.setDiscountAmount(product.getDiscountAmount());
        billFeeResponse.setSubTotalAmount(subTotalAmount);
        billFeeResponse.setTaxAmount(taxAmount);
        billFeeResponse.setAmountWithoutTax(product.getAmountWithoutTax());
        billFeeResponse.setAmountWithTax(product.getAmountWithTax());
        billFeeResponse.setAvailableInvoiceAmount(product.getAvailableInvoiceAmount());
        billFeeResponse.setInvoicedAmount(product.getInvoicedAmount());
        billFeeResponse.setBillId(product.getProductBillId());
        billFeeResponse.setTaxRate(product.getTaxRate());
        billFeeResponse.setBillType(productBill.getBillType());

        return productBill;
    }
}
