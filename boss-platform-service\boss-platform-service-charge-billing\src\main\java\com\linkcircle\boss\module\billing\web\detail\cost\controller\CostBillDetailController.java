package com.linkcircle.boss.module.billing.web.detail.cost.controller;

import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.module.billing.annotation.BillingAuth;
import com.linkcircle.boss.module.billing.web.detail.cost.model.dto.CostBillDetailRequestDTO;
import com.linkcircle.boss.module.billing.web.detail.cost.model.vo.CostBillDetailResponseVO;
import com.linkcircle.boss.module.billing.web.detail.cost.service.CostBillDetailService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2025-06-18 11:04
 * @description 成本账单明细控制器
 */
@Tag(name = "成本账单明细接口", description = "业务平台对接计费平台的成本账单明细接口")
@RestController
@RequestMapping("/billing/api/v3/cost")
@RequiredArgsConstructor
@Slf4j
public class CostBillDetailController {

    private final CostBillDetailService costBillDetailService;

    @Operation(
            summary = "创建成本账单明细",
            description = "业务平台推送成本消费数据到计费平台，生成成本账单明细"
    )
    @PostMapping("/charge-cost-bill-detail")
    @BillingAuth
    public CommonResult<CostBillDetailResponseVO> createCostBillDetail(
            @Parameter(description = "X-AppId", required = true) @RequestHeader("X-AppId") String appId,
            @Parameter(description = "X-Tenant", required = true) @RequestHeader("X-Tenant") Long tenantId,
            @Validated @RequestBody CostBillDetailRequestDTO requestDTO) {
        requestDTO.setTenantId(tenantId);
        return CommonResult.success(costBillDetailService.createCostBillDetail(requestDTO));
    }

}
