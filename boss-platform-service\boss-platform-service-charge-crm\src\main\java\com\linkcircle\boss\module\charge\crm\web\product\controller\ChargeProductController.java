package com.linkcircle.boss.module.charge.crm.web.product.controller;

import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.framework.common.model.PageResult;
import com.linkcircle.boss.module.charge.crm.web.product.model.dto.ChargeProductAddDTO;
import com.linkcircle.boss.module.charge.crm.web.product.model.dto.ChargeProductEditStatusDTO;
import com.linkcircle.boss.module.charge.crm.web.product.model.dto.ChargeProductPageQueryDTO;
import com.linkcircle.boss.module.charge.crm.web.product.model.dto.ChargeProductUpdateDTO;
import com.linkcircle.boss.module.charge.crm.web.product.model.vo.ChargeProductPageVO;
import com.linkcircle.boss.module.charge.crm.web.product.model.vo.ChargeProductVO;
import com.linkcircle.boss.module.charge.crm.web.product.service.IChargeProductService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

/**
 * <p>
 * 产品信息管理 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@RestController
@RequestMapping("/product")
@Validated
@Tag(name = "产品信息管理")
public class ChargeProductController {

    @Autowired
    private IChargeProductService iChargeProductService;

    @PostMapping(value = "/page")
    @Operation(summary = "分页查询产品")
    public CommonResult<PageResult<ChargeProductPageVO>> page(@RequestBody ChargeProductPageQueryDTO params) {
        Integer paymentType = params.getPaymentType();
        if (paymentType != null && 1 != paymentType && 0 != paymentType) {
            return CommonResult.error(400, "支付类型参数错误");
        }
        return CommonResult.success(iChargeProductService.queryProductPage(params));
    }

    @GetMapping(value = "/{id}")
    @Operation(summary = "根据ID查询产品")
    public CommonResult<ChargeProductVO> getById(@PathVariable("id") Long id) {
        return iChargeProductService.getProductById(id);
    }

    @PostMapping(value = "/create")
    @Operation(summary = "创建产品")
    public CommonResult<Long> create(@RequestBody @Valid ChargeProductAddDTO params) {
        return iChargeProductService.createProduct(params);
    }

    @DeleteMapping(value = "/delete")
    @Operation(summary = "删除产品")
    public CommonResult<Long> delete(@RequestParam List<Long> ids) {
        return iChargeProductService.deleteProduct(ids);
    }

    @PutMapping(value = "/update")
    @Operation(summary = "更新产品")
    public CommonResult<Long> update(@Valid @RequestBody ChargeProductUpdateDTO params) {
        return iChargeProductService.updateProduct(params);
    }

    @PutMapping(value = "/change/status")
    @Operation(summary = "修改产品状态")
    public CommonResult<Long> changeStatus(@Valid @RequestBody ChargeProductEditStatusDTO params) {
        return iChargeProductService.changeProductStatus(params);
    }

    @GetMapping(value = "/copy")
    @Operation(summary = "复制产品")
    public CommonResult<Long> copy(@RequestParam("productId") Long productId) {
        return iChargeProductService.copyProduct(productId);
    }

    @PostMapping(value = "/export")
    @Operation(summary = "导出产品")
    public void export(HttpServletResponse response, @RequestBody ChargeProductPageQueryDTO params) throws IOException {
        iChargeProductService.export(response, params);
    }


}
