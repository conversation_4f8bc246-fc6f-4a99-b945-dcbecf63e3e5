package com.linkcircle.boss.module.billing.web.detail.income.service.calculation.strategy.impl;

import com.linkcircle.boss.framework.common.exception.util.ServiceExceptionUtil;
import com.linkcircle.boss.module.billing.api.rate.model.dto.TierRateConfigDTO;
import com.linkcircle.boss.module.billing.constants.ErrorCodeConstants;
import com.linkcircle.boss.module.billing.constants.RateTypeConstant;
import com.linkcircle.boss.module.billing.enums.IncomeRateTypeEnum;
import com.linkcircle.boss.module.billing.enums.OriginalPriceRateTypeEnum;
import com.linkcircle.boss.module.billing.exception.BillingFailException;
import com.linkcircle.boss.module.billing.web.data.model.vo.CyclePeriodResultVO;
import com.linkcircle.boss.module.billing.web.data.service.CyclePeriodCalculateService;
import com.linkcircle.boss.module.billing.web.data.service.RateCacheDataService;
import com.linkcircle.boss.module.billing.web.detail.income.model.dto.ServiceSubscriptionInfoDTO;
import com.linkcircle.boss.module.billing.web.detail.income.service.calculation.strategy.AbstractIncomeRateChargeStrategy;
import com.linkcircle.boss.module.billing.web.detail.income.service.calculation.strategy.context.IncomeRateChargeRequest;
import com.linkcircle.boss.module.billing.web.detail.income.service.calculation.strategy.context.IncomeRateChargeResponse;
import com.linkcircle.boss.module.billing.web.detail.rate.income.service.IncomeRateUsageManageService;
import com.linkcircle.boss.module.billing.web.detail.rate.strategy.context.OriginalPriceCalculateRequest;
import com.linkcircle.boss.module.billing.web.detail.rate.strategy.context.OriginalPriceCalculateResponse;
import com.linkcircle.boss.module.crm.api.customer.account.vo.CustomerAccountVO;
import com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.AccountSubscriptionsVO;
import com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.Coupon;
import com.linkcircle.boss.module.crm.enums.ChargeRateTypeEnum;
import io.github.kk01001.design.pattern.strategy.IStrategy;
import io.github.kk01001.design.pattern.strategy.StrategyFactory;
import io.github.kk01001.design.pattern.strategy.annotation.Strategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-06-19 15:16
 * @description 阶梯费率计费策略
 */
@Slf4j
@Component
@RequiredArgsConstructor
@Strategy(strategyEnum = IncomeRateTypeEnum.class, strategyType = RateTypeConstant.TIERED)
public class IncomeTieredRateChargeStrategy extends AbstractIncomeRateChargeStrategy implements IStrategy<IncomeRateChargeRequest, IncomeRateChargeResponse> {

    private final RateCacheDataService rateCacheDataService;
    private final CyclePeriodCalculateService cyclePeriodCalculateService;
    private final IncomeRateUsageManageService incomeRateUsageManageService;
    private final StrategyFactory strategyFactory;

    @Override
    protected CyclePeriodCalculateService getCyclePeriodCalculateService() {
        return cyclePeriodCalculateService;
    }

    @Override
    public IncomeRateChargeResponse execute(IncomeRateChargeRequest request) {
        log.info("开始执行阶梯费率计费策略");

        // 1. 获取基础数据
        ServiceSubscriptionInfoDTO serviceInfo = request.getServiceSubscriptionInfoDTO();
        CustomerAccountVO customerAccountVO = request.getCustomerAccountVO();
        String accountCurrency = customerAccountVO.getCurrency();
        AccountSubscriptionsVO.Service serviceConfig = serviceInfo.getServiceConfig();
        BigDecimal usage = request.getUsage();

        // 2. 解析阶梯费率配置
        TierRateConfigDTO rateConfig = getTierRateConfigDTO(serviceConfig, accountCurrency);

        // 4. 计算周期和获取累计用量
        Long businessTime = request.getBusinessTime();
        Long subscriptionId = serviceInfo.getSubscriptionsVO().getId();
        CyclePeriodResultVO cyclePeriodResultVO = getBillingCycle(serviceInfo, customerAccountVO, businessTime);

        // 5. 获取当前周期累计用量（包含本次用量）
        BigDecimal currentUsage = getCurrentCycleUsage(subscriptionId, cyclePeriodResultVO, accountCurrency);
        BigDecimal totalUsageWithCurrent = currentUsage.add(usage);

        // 6. 计算目录价和优惠价（调用原价计算策略）
        OriginalPriceCalculateResponse calculateResponse = calculateOriginalPriceByStrategy(totalUsageWithCurrent, currentUsage, usage,
                rateConfig, serviceConfig.getPaymentOptions(), serviceConfig.getCoupons(),
                getTaxRate(serviceInfo), request.enableTax());
        BigDecimal originalPrice = calculateResponse.getOriginalPrice();
        BigDecimal discountedPrice = calculateResponse.getDiscountedPrice();

        // 7. 计算优惠金额
        BigDecimal discountAmount = originalPrice.subtract(discountedPrice);

        // 9. 更新费率用量
        String usageUnit = request.getUsageUnit();
        Boolean inTrail = calculateResponse.getInTrail();
        boolean updateSuccess = incomeRateUsageManageService.updateRateUsage(cyclePeriodResultVO, usage, usageUnit, inTrail);

        if (!updateSuccess) {
            log.error("阶梯费率用量更新失败");
            throw new BillingFailException(ErrorCodeConstants.BILLING_CALCULATION_FAILED, "用量更新失败");
        }
        // Duration billingLockTime = getBillingLockTime(cyclePeriodResultVO);
        // BigDecimal tierUsage = rateCacheDataService.addRateUsage(BillTypeEnum.INCOME, ChargeRateTypeEnum.TIERED,
        //         subscriptionId, customerAccountVO.getAccountId(), serviceConfig.getServiceId(),
        //         generateBillingCycle(cyclePeriodResultVO), usage, usageUnit, billingLockTime, inTrail);
        // log.info("阶梯费率用量更新成功，累计用量: {}", tierUsage);

        // 10. 构建响应结果
        IncomeRateChargeResponse response = IncomeRateChargeResponse.success();
        response.setSuccess(true);
        response.setCurrency(rateConfig.getCurrency());
        response.setUsage(usage);
        response.setUsageUnit(usageUnit);
        response.setCyclePeriodResultVO(cyclePeriodResultVO);
        response.setRateConfig(rateConfig);
        response.setCouponList(serviceConfig.getCoupons());
        convertChargeResponse(response, calculateResponse);

        log.info("阶梯费率计费完成，累计用量: {}, 本次用量: {}, 目录价: {}, 优惠后价格: {}, 优惠金额: {}",
                totalUsageWithCurrent, usage, originalPrice, discountedPrice, discountAmount);

        return response;
    }

    /**
     * 获取当前周期累计用量
     */
    private BigDecimal getCurrentCycleUsage(Long subscriptionId, CyclePeriodResultVO cyclePeriodResult, String currency) {
        if (!cyclePeriodResult.isSuccess()) {
            log.warn("周期计算失败，返回0用量");
            return BigDecimal.ZERO;
        }

        String billingCycle = generateBillingCycle(cyclePeriodResult);
        return incomeRateUsageManageService.getRateUsage(
                ChargeRateTypeEnum.TIERED.getType(),
                subscriptionId,
                null,
                null,
                billingCycle,
                currency
        );
    }

    /**
     * 通过策略计算阶梯费率目录价和优惠价
     *
     * @param totalUsageWithCurrent 包含本次用量的总累计用量
     * @param previousUsage         之前的累计用量
     * @param currentUsage          本次用量
     * @param rateConfig            阶梯费率配置
     * @param paymentOptions        支付方式 0-现金 1-积分
     * @param couponList            优惠券列表
     * @return 本次计费金额
     */
    private OriginalPriceCalculateResponse calculateOriginalPriceByStrategy(BigDecimal totalUsageWithCurrent,
                                                                            BigDecimal previousUsage,
                                                                            BigDecimal currentUsage,
                                                                            TierRateConfigDTO rateConfig,
                                                                            Integer paymentOptions,
                                                                            List<Coupon> couponList,
                                                                            BigDecimal taxRate,
                                                                            boolean enableTax) {
        // 构建原价计算请求
        OriginalPriceCalculateRequest request = new OriginalPriceCalculateRequest();
        request.setTotalUsageWithCurrent(totalUsageWithCurrent);
        request.setPreviousUsage(previousUsage);
        request.setCurrentUsage(currentUsage);
        request.setRateConfig(rateConfig);
        request.setPaymentOptions(paymentOptions);
        request.setCouponList(couponList);
        request.setTaxRate(taxRate);
        request.setCalculateTaxEnabled(enableTax);

        // 调用原价计算策略
        OriginalPriceCalculateResponse response = strategyFactory.execute(OriginalPriceRateTypeEnum.class,
                OriginalPriceRateTypeEnum.TIERED.name(), request);

        if (response.getSuccess()) {
            return response;
        }
        log.error("response: {}, 原价计算失败", response);
        throw ServiceExceptionUtil.exception(ErrorCodeConstants.BILLING_CALCULATION_FAILED, response.getErrorMessage());
    }

}
