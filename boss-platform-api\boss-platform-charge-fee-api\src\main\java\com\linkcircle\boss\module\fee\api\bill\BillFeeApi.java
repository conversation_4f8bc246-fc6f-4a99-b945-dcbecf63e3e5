package com.linkcircle.boss.module.fee.api.bill;

import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.module.fee.constants.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/7/25 10:52
 */
@FeignClient(name = ApiConstants.NAME,
        path = ApiConstants.PREFIX + "/customer/bill-fee",
//        fallbackFactory = SupplierAccountApiFallback.class,
        dismiss404 = true)
@Tag(name = "RPC 服务 - 账单查询 信息")
public interface BillFeeApi {

    @GetMapping("/compute-unpaid-bill-num")
    @Operation(summary = "计算客户未结清账单数量")
    @Parameter(name = "customerId", description = "客户ID",required = true)
    public CommonResult<Long> computeCustomerUnPaidBillNum(@RequestParam(value = "customerId",required = true) Long customerId);


    @GetMapping("/compute-construct-fee")
    @Operation(summary = "计算整个账单的费用")
    @Parameters({
            @Parameter(name = "constructId", description = "合同id",required = true),
            @Parameter(name = "startTime", description = "开始时间",required = false),
            @Parameter(name = "endTime", description = "结束时间",required = false)
    })
    public CommonResult<BigDecimal> computeConstructFee(@RequestParam(value = "constructId",required = true) Long constructId,
                                                        @RequestParam(value = "startTime",required = false) Long startTime,
                                                        @RequestParam(value = "endTime",required = false) Long endTime);
}
