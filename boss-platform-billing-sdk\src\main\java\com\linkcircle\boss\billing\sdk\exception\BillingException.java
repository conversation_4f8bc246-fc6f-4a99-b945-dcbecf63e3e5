package com.linkcircle.boss.billing.sdk.exception;

/**
 * <AUTHOR>
 * @date 2025-07-25 15:00
 * @description 计费SDK异常类
 */
public class BillingException extends RuntimeException {

    /**
     * 错误码
     */
    private final String errorCode;

    public BillingException(String message) {
        super(message);
        this.errorCode = "BILLING_ERROR";
    }

    public BillingException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }

    public BillingException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = "BILLING_ERROR";
    }

    public BillingException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }

    public String getErrorCode() {
        return errorCode;
    }

    @Override
    public String toString() {
        return "BillingException{" +
                "errorCode='" + errorCode + '\'' +
                ", message='" + getMessage() + '\'' +
                '}';
    }
}
