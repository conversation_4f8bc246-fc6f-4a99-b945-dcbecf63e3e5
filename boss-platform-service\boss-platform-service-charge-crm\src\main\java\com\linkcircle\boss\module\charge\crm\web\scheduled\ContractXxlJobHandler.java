package com.linkcircle.boss.module.charge.crm.web.scheduled;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.linkcircle.boss.framework.datapermission.core.util.UserDataPermissionUtils;
import com.linkcircle.boss.framework.tanant.TenantUtils;
import com.linkcircle.boss.module.charge.crm.web.contract.mapper.ContractInfoMapper;
import com.linkcircle.boss.module.charge.crm.web.contract.model.entity.ContractInfoDO;
import com.xxl.job.core.handler.annotation.XxlJob;
import io.github.kk01001.xxljob.annotations.XxlJobRegister;
import io.github.kk01001.xxljob.enums.ExecutorRouteStrategyEnum;
import io.github.kk01001.xxljob.enums.MisfireStrategyEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.List;

/**
 * <AUTHOR> zyuan
 * @data : 2025-06-24
 */
@Slf4j
@Component
public class ContractXxlJobHandler {

    @Resource
    private ContractInfoMapper contractInfoMapper;

    @XxlJob("contractJobHandler")
    @XxlJobRegister(
            cron = "0 0 0 * * *",
            jobDesc = "合同状态更新定时任务",
            author = "admin",
            triggerStatus = 1,
            executorRouteStrategy = ExecutorRouteStrategyEnum.ROUND,
            misfireStrategy = MisfireStrategyEnum.DO_NOTHING
    )
    public void contractJobHandler() {
        long timestamp = LocalDate.now()
                .atStartOfDay(ZoneId.systemDefault())
                .toInstant()
                .toEpochMilli();

        // 处理待开始合同，进行中
        List<ContractInfoDO> contractInfoDOS = TenantUtils.executeIgnore(() ->
                UserDataPermissionUtils.executeIgnore(() ->
                        contractInfoMapper.selectList(new LambdaQueryWrapper<ContractInfoDO>()
                                .eq(ContractInfoDO::getDeleted, false)
                                .eq(ContractInfoDO::getStatus, 0)
                                .lt(ContractInfoDO::getStartTime, timestamp))));
        if (!contractInfoDOS.isEmpty()) {
            for (ContractInfoDO contractInfoDO : contractInfoDOS) {
                log.info("当前合同【" + contractInfoDO.getContractName() + "】原始状态：{}，最新状态: {}",
                        contractInfoDO.getStatus(), 1);
                contractInfoDO.setStatus(1);
            }
            contractInfoMapper.updateBatch(contractInfoDOS);
        }

        // 处理进行中合同，结束合同
        List<ContractInfoDO> contractInfoDOS2 = TenantUtils.executeIgnore(() ->
                UserDataPermissionUtils.executeIgnore(() ->
                        contractInfoMapper.selectList(new LambdaQueryWrapper<ContractInfoDO>()
                                .eq(ContractInfoDO::getDeleted, false)
                                .in(ContractInfoDO::getStatus, 1)
                                .lt(ContractInfoDO::getEndTime, timestamp))));
        if (!contractInfoDOS2.isEmpty()) {
            for (ContractInfoDO contractInfoDO : contractInfoDOS2) {
                if (contractInfoDO.getEndTime() != 0) {
                    log.info("当前合同【" + contractInfoDO.getContractName() + "】原始状态：{}，最新状态: {}",
                            contractInfoDO.getStatus(), 2);
                    contractInfoDO.setStatus(2);
                }
            }
            contractInfoMapper.updateBatch(contractInfoDOS2);
        }
    }
}
