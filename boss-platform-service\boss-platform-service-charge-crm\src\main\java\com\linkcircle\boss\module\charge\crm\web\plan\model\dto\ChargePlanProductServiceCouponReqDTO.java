package com.linkcircle.boss.module.charge.crm.web.plan.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;


/**
 * 计划产品服务优惠管理DTO
 *
 * <AUTHOR>
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "创建/编辑计划服务优惠券请求")
public class ChargePlanProductServiceCouponReqDTO {

    @Schema(description = "计划产品服务表id")
    private Long planProductServiceId;

    @Schema(description = "优惠id 手动添加的优惠无id")
    private Long couponId;

    @NotBlank(message = "优惠名称不能为空")
    @Schema(description = "优惠名称")
    private String couponName;

    @NotNull(message = "优惠类型不能为空")
    @Schema(description = "优惠类型 0:固定金额，1：百分比")
    private Integer couponType;

    @Schema(description = "优惠百分比")
    private BigDecimal couponPercentage;

    @Schema(description = "优惠金额")
    private BigDecimal couponAmount;

    @Schema(description = "优惠顺序")
    private Integer sequence;

    @Schema(description = "优惠范围 0.全部 1.套餐内 2.套餐外")
    private Integer scope;
}
