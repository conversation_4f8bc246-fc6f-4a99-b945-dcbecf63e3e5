package com.linkcircle.boss.module.billing.web.detail.rate.income.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.linkcircle.boss.module.billing.web.data.model.vo.CyclePeriodResultVO;
import com.linkcircle.boss.module.billing.web.detail.rate.income.mapper.IncomeFixedRateCycleStatusMapper;
import com.linkcircle.boss.module.billing.web.detail.rate.income.mapper.IncomePackageRateUsageMapper;
import com.linkcircle.boss.module.billing.web.detail.rate.income.mapper.IncomeTierRateUsageMapper;
import com.linkcircle.boss.module.billing.web.detail.rate.income.mapper.IncomeUsageRateUsageMapper;
import com.linkcircle.boss.module.billing.web.detail.rate.income.model.entity.IncomeFixedRateCycleStatusDO;
import com.linkcircle.boss.module.billing.web.detail.rate.income.model.entity.IncomePackageRateUsageDO;
import com.linkcircle.boss.module.billing.web.detail.rate.income.model.entity.IncomeTierRateUsageDO;
import com.linkcircle.boss.module.billing.web.detail.rate.income.model.entity.IncomeUsageRateUsageDO;
import com.linkcircle.boss.module.billing.web.detail.rate.income.service.IncomeRateUsageManageService;
import com.linkcircle.boss.module.crm.enums.ChargeRateTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-06-23 09:02
 * @description 收入-费率用量管理服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IncomeRateUsageManageServiceImpl implements IncomeRateUsageManageService {

    private final IncomeFixedRateCycleStatusMapper incomeFixedRateCycleStatusMapper;
    private final IncomeTierRateUsageMapper incomeTierRateUsageMapper;
    private final IncomePackageRateUsageMapper incomePackageRateUsageMapper;
    private final IncomeUsageRateUsageMapper incomeUsageRateUsageMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateRateUsage(CyclePeriodResultVO cyclePeriodResult,
                                   BigDecimal usage,
                                   String usageUnit,
                                   Boolean inTrail) {

        // 试用期
        if (Boolean.TRUE.equals(inTrail)) {
            return true;
        }

        if (!cyclePeriodResult.isSuccess()) {
            log.warn("周期计算失败，无法更新费率用量: {}", cyclePeriodResult.getErrorMessage());
            return false;
        }

        ChargeRateTypeEnum rateTypeEnum = cyclePeriodResult.getRateTypeEnum();
        if (rateTypeEnum == null) {
            log.error("不支持的费率类型: {}", rateTypeEnum);
            return false;
        }
        // TODO Lock Async
        String billingCycle = generateBillingCycle(cyclePeriodResult, cyclePeriodResult.getTimezone());
        Long subscriptionId = cyclePeriodResult.getSubscriptionId();
        Long accountId = cyclePeriodResult.getAccountId();
        Long serviceId = cyclePeriodResult.getServiceId();
        String currency = cyclePeriodResult.getCurrency();
        return switch (rateTypeEnum) {
            case FIXED -> updateFixedRateStatus(subscriptionId, billingCycle, cyclePeriodResult, currency);
            case TIERED ->
                    updateTierRateUsage(subscriptionId, billingCycle, cyclePeriodResult, usage, usageUnit, currency);
            case PACKAGE ->
                    updatePackageRateUsage(subscriptionId, billingCycle, cyclePeriodResult, usage, usageUnit, currency);
            case USAGE ->
                    updateUsageRateUsage(accountId, serviceId, billingCycle, cyclePeriodResult, usage, usageUnit, currency);
        };
    }

    @Override
    public BigDecimal getRateUsage(Integer rateType,
                                   Long subscriptionId,
                                   Long accountId,
                                   Long serviceId,
                                   String billingCycle,
                                   String currency) {

        ChargeRateTypeEnum rateTypeEnum = ChargeRateTypeEnum.getByType(rateType);
        if (rateTypeEnum == null) {
            log.error("不支持的费率类型: {}", rateType);
            return BigDecimal.ZERO;
        }

        return switch (rateTypeEnum) {
            case FIXED -> null;
            case TIERED -> getTierRateUsage(subscriptionId, billingCycle, currency);
            case PACKAGE -> getPackageRateUsage(subscriptionId, billingCycle, currency);
            case USAGE -> getUsageRateUsage(accountId, serviceId, billingCycle, currency);
        };
    }

    @Override
    public boolean isFixedRateBilled(Long subscriptionId, String billingCycle, String currency) {
        LambdaQueryWrapper<IncomeFixedRateCycleStatusDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(IncomeFixedRateCycleStatusDO::getId);
        queryWrapper.eq(IncomeFixedRateCycleStatusDO::getSubscriptionId, subscriptionId)
                .eq(IncomeFixedRateCycleStatusDO::getBillingCycle, billingCycle)
                .eq(IncomeFixedRateCycleStatusDO::getCurrency, currency);

        IncomeFixedRateCycleStatusDO statusDO = incomeFixedRateCycleStatusMapper.selectOne(queryWrapper);
        return statusDO != null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean markFixedRateBilled(Long subscriptionId, CyclePeriodResultVO cyclePeriodResult, String currency) {
        if (!cyclePeriodResult.isSuccess()) {
            log.warn("周期计算失败，无法标记固定费率已计费: {}", cyclePeriodResult.getErrorMessage());
            return false;
        }

        String billingCycle = cyclePeriodResult.getBillingCycle();
        long currentTime = System.currentTimeMillis();

        LambdaUpdateWrapper<IncomeFixedRateCycleStatusDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(IncomeFixedRateCycleStatusDO::getSubscriptionId, subscriptionId)
                .eq(IncomeFixedRateCycleStatusDO::getBillingCycle, billingCycle)
                .eq(IncomeFixedRateCycleStatusDO::getCurrency, currency)
                .set(IncomeFixedRateCycleStatusDO::getIsBilled, true)
                .set(IncomeFixedRateCycleStatusDO::getBillingTime, currentTime);

        int updateCount = incomeFixedRateCycleStatusMapper.update(null, updateWrapper);

        if (updateCount > 0) {
            log.info("标记固定费率已计费成功, subscriptionId: {}, billingCycle: {}", subscriptionId, billingCycle);
            return true;
        }

        log.warn("标记固定费率已计费失败, subscriptionId: {}, billingCycle: {}", subscriptionId, billingCycle);
        return false;
    }

    @Override
    public boolean isTieredRateBilled(Long subscriptionId, String billingCycle, String currency) {
        LambdaQueryWrapper<IncomeTierRateUsageDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(IncomeTierRateUsageDO::getId);
        queryWrapper.eq(IncomeTierRateUsageDO::getSubscriptionId, subscriptionId)
                .eq(IncomeTierRateUsageDO::getBillingCycle, billingCycle)
                .eq(IncomeTierRateUsageDO::getCurrency, currency);

        IncomeTierRateUsageDO statusDO = incomeTierRateUsageMapper.selectOne(queryWrapper);
        return statusDO != null;
    }

    /**
     * 更新固定费率状态
     */
    private boolean updateFixedRateStatus(Long subscriptionId, String billingCycle,
                                          CyclePeriodResultVO cyclePeriodResult, String currency) {
        try {
            // 检查是否已存在记录
            LambdaQueryWrapper<IncomeFixedRateCycleStatusDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(IncomeFixedRateCycleStatusDO::getSubscriptionId, subscriptionId)
                    .eq(IncomeFixedRateCycleStatusDO::getBillingCycle, billingCycle)
                    .eq(IncomeFixedRateCycleStatusDO::getCurrency, currency);

            IncomeFixedRateCycleStatusDO existingRecord = incomeFixedRateCycleStatusMapper.selectOne(queryWrapper);

            if (existingRecord == null) {
                // 创建新记录
                IncomeFixedRateCycleStatusDO statusDO = IncomeFixedRateCycleStatusDO.builder()
                        .subscriptionId(subscriptionId)
                        .billingCycle(billingCycle)
                        .billingCycleStart(cyclePeriodResult.getCycleStartTime())
                        .billingCycleEnd(cyclePeriodResult.getCycleEndTime())
                        .isBilled(false)
                        .currency(currency)
                        .billingTime(System.currentTimeMillis())
                        .createTime(System.currentTimeMillis())
                        .build();

                int insertCount = incomeFixedRateCycleStatusMapper.insert(statusDO);
                log.info("创建固定费率周期状态记录, subscriptionId: {}, billingCycle: {}, result: {}",
                        subscriptionId, billingCycle, insertCount > 0 ? "成功" : "失败");
                return insertCount > 0;
            }

            log.debug("固定费率周期状态记录已存在, subscriptionId: {}, billingCycle: {}", subscriptionId, billingCycle);
            return true;

        } catch (Exception e) {
            log.error("更新固定费率状态异常", e);
            return false;
        }
    }

    /**
     * 更新阶梯费率用量
     */
    private boolean updateTierRateUsage(Long subscriptionId, String billingCycle,
                                        CyclePeriodResultVO cyclePeriodResult, BigDecimal usage,
                                        String usageUnit, String currency) {
        try {
            // 检查是否已存在记录
            LambdaQueryWrapper<IncomeTierRateUsageDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(IncomeTierRateUsageDO::getSubscriptionId, subscriptionId)
                    .eq(IncomeTierRateUsageDO::getBillingCycle, billingCycle)
                    .eq(IncomeTierRateUsageDO::getCurrency, currency);

            IncomeTierRateUsageDO existingRecord = incomeTierRateUsageMapper.selectOne(queryWrapper);

            if (existingRecord == null) {
                // 创建新记录
                IncomeTierRateUsageDO usageDO = IncomeTierRateUsageDO.builder()
                        .subscriptionId(subscriptionId)
                        .billingCycle(billingCycle)
                        .billingCycleStart(cyclePeriodResult.getCycleStartTime())
                        .billingCycleEnd(cyclePeriodResult.getCycleEndTime())
                        .totalUsage(usage)
                        .usageUnit(usageUnit)
                        .currency(currency)
                        .billingTime(System.currentTimeMillis())
                        .createTime(System.currentTimeMillis())
                        .build();

                int insertCount = incomeTierRateUsageMapper.insert(usageDO);
                log.info("创建阶梯费率用量记录, subscriptionId: {}, billingCycle: {}, usage: {}, result: {}",
                        subscriptionId, billingCycle, usage, insertCount > 0 ? "成功" : "失败");
                return insertCount > 0;
            }

            // 更新累计用量
            BigDecimal newTotalUsage = existingRecord.getTotalUsage().add(usage);
            LambdaUpdateWrapper<IncomeTierRateUsageDO> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(IncomeTierRateUsageDO::getId, existingRecord.getId())
                    .set(IncomeTierRateUsageDO::getTotalUsage, newTotalUsage)
                    .set(IncomeTierRateUsageDO::getBillingTime, System.currentTimeMillis());

            int updateCount = incomeTierRateUsageMapper.update(null, updateWrapper);
            log.info("更新阶梯费率用量, subscriptionId: {}, billingCycle: {}, 原用量: {}, 新增用量: {}, 总用量: {}, result: {}",
                    subscriptionId, billingCycle, existingRecord.getTotalUsage(), usage, newTotalUsage,
                    updateCount > 0 ? "成功" : "失败");
            return updateCount > 0;

        } catch (Exception e) {
            log.error("更新阶梯费率用量异常", e);
            return false;
        }
    }

    /**
     * 更新套餐费率用量
     */
    private boolean updatePackageRateUsage(Long subscriptionId,
                                           String billingCycle,
                                           CyclePeriodResultVO cyclePeriodResult,
                                           BigDecimal usage,
                                           String usageUnit,
                                           String currency) {
        try {
            // 检查是否已存在记录
            LambdaQueryWrapper<IncomePackageRateUsageDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(IncomePackageRateUsageDO::getSubscriptionId, subscriptionId)
                    .eq(IncomePackageRateUsageDO::getBillingCycle, billingCycle)
                    .eq(IncomePackageRateUsageDO::getCurrency, currency);

            IncomePackageRateUsageDO existingRecord = incomePackageRateUsageMapper.selectOne(queryWrapper);

            if (existingRecord == null) {
                // 创建新记录
                IncomePackageRateUsageDO usageDO = IncomePackageRateUsageDO.builder()
                        .subscriptionId(subscriptionId)
                        .billingCycle(billingCycle)
                        .billingCycleStart(cyclePeriodResult.getCycleStartTime())
                        .billingCycleEnd(cyclePeriodResult.getCycleEndTime())
                        .totalUsage(usage)
                        .usageUnit(usageUnit)
                        .currency(currency)
                        .billingTime(System.currentTimeMillis())
                        .createTime(System.currentTimeMillis())
                        .build();

                int insertCount = incomePackageRateUsageMapper.insert(usageDO);
                log.info("创建套餐费率用量记录, subscriptionId: {}, billingCycle: {}, usage: {}, result: {}",
                        subscriptionId, billingCycle, usage, insertCount > 0 ? "成功" : "失败");
                return insertCount > 0;
            }

            // 更新累计用量
            BigDecimal newTotalUsage = existingRecord.getTotalUsage().add(usage);
            LambdaUpdateWrapper<IncomePackageRateUsageDO> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(IncomePackageRateUsageDO::getId, existingRecord.getId())
                    .set(IncomePackageRateUsageDO::getTotalUsage, newTotalUsage)
                    .set(IncomePackageRateUsageDO::getBillingTime, System.currentTimeMillis());

            int updateCount = incomePackageRateUsageMapper.update(null, updateWrapper);
            log.info("更新套餐费率用量, subscriptionId: {}, billingCycle: {}, 原用量: {}, 新增用量: {}, 总用量: {}, result: {}",
                    subscriptionId, billingCycle, existingRecord.getTotalUsage(), usage, newTotalUsage,
                    updateCount > 0 ? "成功" : "失败");
            return updateCount > 0;

        } catch (Exception e) {
            log.error("更新套餐费率用量异常", e);
            return false;
        }
    }

    /**
     * 更新按量费率用量
     */
    private boolean updateUsageRateUsage(Long accountId, Long serviceId, String billingCycle,
                                         CyclePeriodResultVO cyclePeriodResult, BigDecimal usage,
                                         String usageUnit, String currency) {
        try {
            // 检查是否已存在记录
            LambdaQueryWrapper<IncomeUsageRateUsageDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(IncomeUsageRateUsageDO::getAccountId, accountId)
                    .eq(IncomeUsageRateUsageDO::getServiceId, serviceId)
                    .eq(IncomeUsageRateUsageDO::getBillingCycle, billingCycle)
                    .eq(IncomeUsageRateUsageDO::getCurrency, currency);

            IncomeUsageRateUsageDO existingRecord = incomeUsageRateUsageMapper.selectOne(queryWrapper);

            if (existingRecord == null) {
                // 创建新记录
                IncomeUsageRateUsageDO usageDO = IncomeUsageRateUsageDO.builder()
                        .accountId(accountId)
                        .serviceId(serviceId)
                        .billingCycle(billingCycle)
                        .billingCycleStart(cyclePeriodResult.getCycleStartTime())
                        .billingCycleEnd(cyclePeriodResult.getCycleEndTime())
                        .totalUsage(usage)
                        .usageUnit(usageUnit)
                        .currency(currency)
                        .billingTime(System.currentTimeMillis())
                        .createTime(System.currentTimeMillis())
                        .build();

                int insertCount = incomeUsageRateUsageMapper.insert(usageDO);
                log.info("创建按量费率用量记录, accountId: {}, serviceId: {}, billingCycle: {}, usage: {}, result: {}",
                        accountId, serviceId, billingCycle, usage, insertCount > 0 ? "成功" : "失败");
                return insertCount > 0;
            }

            // 更新累计用量
            BigDecimal newTotalUsage = existingRecord.getTotalUsage().add(usage);
            LambdaUpdateWrapper<IncomeUsageRateUsageDO> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(IncomeUsageRateUsageDO::getId, existingRecord.getId())
                    .set(IncomeUsageRateUsageDO::getTotalUsage, newTotalUsage)
                    .set(IncomeUsageRateUsageDO::getBillingTime, System.currentTimeMillis());

            int updateCount = incomeUsageRateUsageMapper.update(null, updateWrapper);
            log.info("更新按量费率用量, accountId: {}, serviceId: {}, billingCycle: {}, 原用量: {}, 新增用量: {}, 总用量: {}, result: {}",
                    accountId, serviceId, billingCycle, existingRecord.getTotalUsage(), usage, newTotalUsage,
                    updateCount > 0 ? "成功" : "失败");
            return updateCount > 0;

        } catch (Exception e) {
            log.error("更新按量费率用量异常", e);
            return false;
        }
    }

    /**
     * 获取阶梯费率用量
     */
    private BigDecimal getTierRateUsage(Long subscriptionId, String billingCycle, String currency) {
        LambdaQueryWrapper<IncomeTierRateUsageDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IncomeTierRateUsageDO::getSubscriptionId, subscriptionId)
                .eq(IncomeTierRateUsageDO::getBillingCycle, billingCycle)
                .eq(IncomeTierRateUsageDO::getCurrency, currency);

        IncomeTierRateUsageDO usageDO = incomeTierRateUsageMapper.selectOne(queryWrapper);
        return usageDO != null ? usageDO.getTotalUsage() : BigDecimal.ZERO;
    }

    /**
     * 获取套餐费率用量
     */
    private BigDecimal getPackageRateUsage(Long subscriptionId, String billingCycle, String currency) {
        LambdaQueryWrapper<IncomePackageRateUsageDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IncomePackageRateUsageDO::getSubscriptionId, subscriptionId)
                .eq(IncomePackageRateUsageDO::getBillingCycle, billingCycle)
                .eq(IncomePackageRateUsageDO::getCurrency, currency);

        IncomePackageRateUsageDO usageDO = incomePackageRateUsageMapper.selectOne(queryWrapper);
        return usageDO != null ? usageDO.getTotalUsage() : BigDecimal.ZERO;
    }

    /**
     * 获取按量费率用量
     */
    private BigDecimal getUsageRateUsage(Long accountId, Long serviceId, String billingCycle, String currency) {
        LambdaQueryWrapper<IncomeUsageRateUsageDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IncomeUsageRateUsageDO::getAccountId, accountId)
                .eq(IncomeUsageRateUsageDO::getServiceId, serviceId)
                .eq(IncomeUsageRateUsageDO::getBillingCycle, billingCycle)
                .eq(IncomeUsageRateUsageDO::getCurrency, currency);

        IncomeUsageRateUsageDO usageDO = incomeUsageRateUsageMapper.selectOne(queryWrapper);
        return usageDO != null ? usageDO.getTotalUsage() : BigDecimal.ZERO;
    }

    /**
     * 生成计费周期标识
     */
    private String generateBillingCycle(CyclePeriodResultVO cyclePeriodResult, String timezone) {
        return cyclePeriodResult.getBillingCycle();
    }

}
