package com.linkcircle.boss.module.charge.crm.web.scheduled;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.linkcircle.boss.framework.datapermission.core.util.UserDataPermissionUtils;
import com.linkcircle.boss.framework.tanant.TenantUtils;
import com.linkcircle.boss.module.charge.crm.web.project.mapper.ProjectInfoMapper;
import com.linkcircle.boss.module.charge.crm.web.project.model.entity.ProjectInfoDO;
import com.xxl.job.core.handler.annotation.XxlJob;
import io.github.kk01001.xxljob.annotations.XxlJobRegister;
import io.github.kk01001.xxljob.enums.ExecutorRouteStrategyEnum;
import io.github.kk01001.xxljob.enums.MisfireStrategyEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.List;

/**
 * 项目 定时任务
 * 处理项目开工日期 结束日期
 *
 * <AUTHOR> zyuan
 * @data : 2025-06-12
 */
@Slf4j
@Component
public class ProjectXxlJobHandler {

    @Resource
    private ProjectInfoMapper projectInfoMapper;

    @XxlJob("projectJobHandler")
    @XxlJobRegister(
            cron = "0 0 0 * * *",
            jobDesc = "项目状态更新定时任务",
            author = "admin",
            triggerStatus = 1,
            executorRouteStrategy = ExecutorRouteStrategyEnum.ROUND,
            misfireStrategy = MisfireStrategyEnum.DO_NOTHING
    )
    public void projectJobHandler() {
        long timestamp = LocalDate.now()
                .atStartOfDay(ZoneId.systemDefault())
                .toInstant()
                .toEpochMilli();

        // 处理待开始项目，进行中
        List<ProjectInfoDO> projectInfoDOS1 = TenantUtils.executeIgnore(() ->
                UserDataPermissionUtils.executeIgnore(() ->
                        projectInfoMapper.selectList(new LambdaQueryWrapper<ProjectInfoDO>()
                                .eq(ProjectInfoDO::getDeleted, false)
                                .eq(ProjectInfoDO::getStatus, 0)
                                .lt(ProjectInfoDO::getStartTime, timestamp))));
        if (!projectInfoDOS1.isEmpty()) {
            for (ProjectInfoDO projectInfoDO : projectInfoDOS1) {
                log.info("当前项目【" + projectInfoDO.getProjectName() + "】原始状态：{}，最新状态: {}",
                        projectInfoDO.getStatus(), 1);
                projectInfoDO.setStatus(1);
            }
            projectInfoMapper.updateBatch(projectInfoDOS1);
        }

        // 处理进行中项目，结项
        List<ProjectInfoDO> projectInfoDOS2 = TenantUtils.executeIgnore(() ->
                UserDataPermissionUtils.executeIgnore(() ->
                        projectInfoMapper.selectList(new LambdaQueryWrapper<ProjectInfoDO>()
                                .eq(ProjectInfoDO::getDeleted, false)
                                .in(ProjectInfoDO::getStatus, 1, 2)
                                .lt(ProjectInfoDO::getEndTime, timestamp))));
        if (!projectInfoDOS2.isEmpty()) {
            for (ProjectInfoDO projectInfoDO : projectInfoDOS2) {
                if (projectInfoDO.getEndTime() != 0) {
                    log.info("当前项目【" + projectInfoDO.getProjectName() + "】原始状态：{}，最新状态: {}",
                            projectInfoDO.getStatus(), 3);
                    projectInfoDO.setStatus(3);
                }
            }
            projectInfoMapper.updateBatch(projectInfoDOS2);
        }
    }

    public static void main(String[] args) {
//        long timestamp = LocalDate.now()
//                .atStartOfDay(ZoneId.systemDefault())
//                .toInstant()
//                .toEpochMilli();
//        System.out.println(timestamp);
//        String formattedDate = DateUtil.date(timestamp).toString("yyyy-MM-dd HH:mm:ss");
//        System.out.println(formattedDate);

    }
}
