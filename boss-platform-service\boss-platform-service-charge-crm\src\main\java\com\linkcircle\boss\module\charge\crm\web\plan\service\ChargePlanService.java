package com.linkcircle.boss.module.charge.crm.web.plan.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.linkcircle.boss.framework.common.model.PageResult;
import com.linkcircle.boss.module.charge.crm.web.plan.model.dto.ChargePlanCreateReqDTO;
import com.linkcircle.boss.module.charge.crm.web.plan.model.dto.ChargePlanPageReqDTO;
import com.linkcircle.boss.module.charge.crm.web.plan.model.dto.ChargePlanSelectDTO;
import com.linkcircle.boss.module.charge.crm.web.plan.model.entity.ChargePlanDO;
import com.linkcircle.boss.module.charge.crm.web.plan.model.vo.ChargePlanDetailVO;
import com.linkcircle.boss.module.charge.crm.web.plan.model.vo.ChargePlanPageVO;
import com.linkcircle.boss.module.crm.api.common.dto.CommonDTO;
import com.linkcircle.boss.module.crm.api.common.vo.CommonVO;

import java.util.List;

/**
 * 计划 Service 接口
 *
 * <AUTHOR>
 */
public interface ChargePlanService extends IService<ChargePlanDO> {

    PageResult<ChargePlanPageVO> page(ChargePlanPageReqDTO dto);

    ChargePlanDetailVO detail(Long id);

    Long create(ChargePlanCreateReqDTO dto);

    boolean update(ChargePlanCreateReqDTO dto);

    boolean changeStatus(Long id, Integer status);

    boolean delete(Long id);

    void copy(Long id);

    List<CommonVO> findNameByIds(CommonDTO commonDTO);

    List<ChargePlanDO> queryAll(ChargePlanSelectDTO dto);
}