package com.linkcircle.boss.module.charge.crm.web.plan.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.linkcircle.boss.framework.tanant.TenantBaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 计划信息表实体类
 *
 * <AUTHOR>
 */
@TableName("charge_plan")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChargePlanDO extends TenantBaseDO {
    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 计划名称
     */
    private String planName;

    /**
     * 描述
     */
    private String remark;

    /**
     * 支付方式，0:现金，1：积分
     */
    private Integer paymentOptions;

    /**
     * 货币代码（ISO 4217 标准，如 CNY 表示人民币）
     */
    private String currencyCode;

    /**
     * 状态，0：未激活，1：激活，2：存档
     */
    private Integer status;

    /**
     * 支付类型，0：预付费，1后付费
     */
    private Integer paymentType;

    /**
     * 产品json
     */
    private String productJson;
}
