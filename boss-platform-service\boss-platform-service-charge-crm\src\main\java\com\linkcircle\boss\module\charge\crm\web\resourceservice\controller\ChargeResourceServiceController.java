package com.linkcircle.boss.module.charge.crm.web.resourceservice.controller;


import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.framework.common.model.PageParam;
import com.linkcircle.boss.framework.common.model.PageResult;
import com.linkcircle.boss.framework.excel.core.util.ExcelUtils;
import com.linkcircle.boss.module.charge.crm.web.resourceservice.model.dto.ChargeResourceServiceAddDTO;
import com.linkcircle.boss.module.charge.crm.web.resourceservice.model.dto.ChargeResourceServiceEditDTO;
import com.linkcircle.boss.module.charge.crm.web.resourceservice.model.dto.ChargeResourceServiceQueryDTO;
import com.linkcircle.boss.module.charge.crm.web.resourceservice.model.vo.*;
import com.linkcircle.boss.module.charge.crm.web.resourceservice.service.IChargeResourceServiceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;


/**
 * <p>
 * 资源服务基本信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
@Tag(name = "资源服务")
@RestController
@RequiredArgsConstructor
@RequestMapping("/resource-service")
public class ChargeResourceServiceController {

    private final IChargeResourceServiceService chargeResourceServiceService;

    @PostMapping(value = "/page")
    @Operation(summary = "资源服务分页查询")
//    @PreAuthorize("@ss.hasPermission('charge-crm:resource-service:page')")
    public CommonResult<PageResult<ChargeResourceServiceVO>> list(@Valid @RequestBody ChargeResourceServiceQueryDTO queryDTO) {
        PageResult<ChargeResourceServiceVO> pages = chargeResourceServiceService.pageQuery(queryDTO);
        return CommonResult.success(pages);
    }

    @PostMapping(value = "/creat")
    @Operation(summary = "资源服务新增接口")
//    @PreAuthorize("@ss.hasPermission('charge-crm:resource-service:creat')")
    public CommonResult<?> creat(@Valid @RequestBody ChargeResourceServiceAddDTO addDTO) {
        return chargeResourceServiceService.create(addDTO);
    }

    @GetMapping(value = "/getVersionList")
    @Operation(summary = "当前服务版本列表接口")
//    @PreAuthorize("@ss.hasPermission('charge-crm:resource-service:getVersionList')")
    public CommonResult<List<ChargeResourceServiceVersionVO>> creat(@RequestParam(value = "serviceId") Long ServiceId) {
        return chargeResourceServiceService.getVersionList(ServiceId);
    }

    @GetMapping(value = "/getVersionInfoById")
    @Operation(summary = "根据版本id查询版本信息接口")
//    @PreAuthorize("@ss.hasPermission('charge-crm:resource-service:getVersionInfoById')")
    public CommonResult<ChargeResourceServiceVersionInfoVO> getVersionInfoById(@RequestParam(value = "versionId") Long versionId) {
        return chargeResourceServiceService.getVersionInfoById(versionId);
    }

    @GetMapping(value = "/editVersionName")
    @Operation(summary = "编辑版本名称接口")
//    @PreAuthorize("@ss.hasPermission('charge-crm:resource-service:getVersionInfoById')")
    public CommonResult<?> editVersionName(@RequestParam(value = "versionId") Long versionId, @RequestParam(value = "versionName") String versionName) {
        return chargeResourceServiceService.editVersionName(versionId, versionName);
    }

    @GetMapping(value = "/getMaxVersionInfoById")
    @Operation(summary = "查询最大版本号版本信息接口(点击编辑服务查询的接口)")
//    @PreAuthorize("@ss.hasPermission('charge-crm:resource-service:getMaxVersionInfoById')")
    public CommonResult<ChargeResourceServiceVersionInfoVO> getMaxVersionInfoById(@RequestParam(value = "serviceId") Long serviceId) {
        return chargeResourceServiceService.getMaxVersionInfoById(serviceId);
    }

    @PostMapping(value = "/edit")
    @Operation(summary = "资源服务编辑接口")
//    @PreAuthorize("@ss.hasPermission('charge-crm:resource-service:edit')")
    public CommonResult<?> creat(@Valid @RequestBody ChargeResourceServiceEditDTO editDTO) {
        return chargeResourceServiceService.edit(editDTO);
    }

    @GetMapping(value = "/addVersion")
    @Operation(summary = "资源服务新增版本接口")
//    @PreAuthorize("@ss.hasPermission('charge-crm:resource-service:addVersion')")
    public CommonResult<?> addVersion(@RequestParam(value = "serviceId") Long ServiceId) {
        return chargeResourceServiceService.addVersion(ServiceId);
    }

    @PostMapping(value = "/activationBatch")
    @Operation(summary = "批量激活资源服务接口")
    @Parameter(
            description = "服务ID列表，支持批量操作",
            required = true,
            example = "[1001, 1002]"
    )
//    @PreAuthorize("@ss.hasPermission('charge-crm:resource-service:activation')")
    public CommonResult<?> activationBatch(@RequestBody List<Long> ServiceIds) {
        return chargeResourceServiceService.activation(ServiceIds);
    }

    @PostMapping(value = "/deactivateBatch")
    @Operation(summary = "批量取消激活资源服务接口")
    @Parameter(
            description = "服务ID列表，支持批量操作",
            required = true,
            example = "[1001, 1002]"
    )
//    @PreAuthorize("@ss.hasPermission('charge-crm:resource-service:deactivate')")
    public CommonResult<?> deactivate(@RequestBody List<Long> ServiceIds) {
        return chargeResourceServiceService.deactivate(ServiceIds);
    }

    @PostMapping(value = "/archiveBatch")
    @Parameter(
            description = "服务ID列表，支持批量操作",
            required = true,
            example = "[1001, 1002]"
    )
    @Operation(summary = "批量存档资源服务接口")
//    @PreAuthorize("@ss.hasPermission('charge-crm:resource-service:archive')")
    public CommonResult<?> archive(@RequestBody List<Long> ServiceIds) {
        return chargeResourceServiceService.archive(ServiceIds);
    }

    @PostMapping(value = "/cancelArchiveBatch")
    @Operation(summary = "批量取消存档资源服务接口")
    @Parameter(
            description = "服务ID列表，支持批量操作",
            required = true,
            example = "[1001, 1002]"
    )
//    @PreAuthorize("@ss.hasPermission('charge-crm:resource-service:cancelArchive')")
    public CommonResult<?> cancelArchive(@RequestBody List<Long> ServiceIds) {
        return chargeResourceServiceService.cancelArchive(ServiceIds);
    }

    @PostMapping(value = "/deleteBatch")
    @Parameter(
            description = "服务ID列表，支持批量操作",
            required = true,
            example = "[1001, 1002]"
    )
    @Operation(summary = "批量删除资源服务接口(versionId 数组)")
//    @PreAuthorize("@ss.hasPermission('charge-crm:resource-service:delete')")
    public CommonResult<?> delete(@RequestBody List<Long> versionIds) {
        return chargeResourceServiceService.delete(versionIds);
    }

    @GetMapping(value = "/configScale")
    @Operation(summary = "配置量表")
//    @PreAuthorize("@ss.hasPermission('charge-crm:resource-service:configScale')")
    public CommonResult<?> configScale(@RequestParam(value = "serviceId") Long ServiceId, @RequestParam(value = "scaleId") Long scaleId) {
        return chargeResourceServiceService.configScale(ServiceId, scaleId);
    }

    @GetMapping(value = "/copy")
    @Operation(summary = "复制资源服务接口(暂时废弃)")
//    @PreAuthorize("@ss.hasPermission('charge-crm:resource-service:copy')")
    public CommonResult<?> copy(@RequestParam(value = "serviceId") Long ServiceId) {
        return chargeResourceServiceService.copy(ServiceId);
    }

    @PostMapping(value = "/export")
    @Operation(summary = "导出资源服务接口")
//    @PreAuthorize("@ss.hasPermission('charge-crm:resource-service:export')")
    public void export(HttpServletResponse response, @RequestBody ChargeResourceServiceQueryDTO queryDTO) throws IOException {
        queryDTO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ChargeResourceServiceVO> list = chargeResourceServiceService.pageQuery(queryDTO).getList();
        ExcelUtils.write(response, "资源服务.xls", "资源服务列表", ChargeResourceServiceVO.class,
                list);
    }

    @GetMapping(value = "/getVersionInfoListByName")
    @Operation(summary = "根据服务名称查询当前服务名称下所有版本信息")
//    @PreAuthorize("@ss.hasPermission('charge-crm:resource-service:getVersionInfoListByName')")
    public CommonResult<List<ChargeResourceServiceGroupVO>> getVersionInfoListByName(@RequestParam(value = "serviceName") String serviceName) {
        return chargeResourceServiceService.getVersionInfoListByName(serviceName);
    }

    @GetMapping(value = "/getResourceServiceByCurrencyCode")
    @Operation(summary = "根据货币编码查询激活资源服务(与版本)")
//    @PreAuthorize("@ss.hasPermission('charge-crm:charge-purchase:getResourceServiceByCurrencyCode')")
    public CommonResult<List<ChargeResourceServiceVersionInfoVO>> getResourceServiceByCurrencyCode(@RequestParam(value = "CurrencyCode", required = false) String CurrencyCode) {
        return chargeResourceServiceService.getResourceServiceByCurrencyCode(CurrencyCode);
    }

    @GetMapping(value = "/getAllResourceService")
    @Operation(summary = "查询所有已激活资源服务")
//    @PreAuthorize("@ss.hasPermission('charge-crm:charge-purchase:getResourceServiceByCurrencyCode')")
    public CommonResult<List<ChargeResourceServiceDropDownVO>> getAllResourceService() {
        return chargeResourceServiceService.getAllResourceService();
    }
}
