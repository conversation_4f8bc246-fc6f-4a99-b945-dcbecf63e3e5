package com.linkcircle.boss.module.charge.crm.web.subscribe.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 订阅持续时间表实体类
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChargeSubscriptionsTimeDetailsUpdateDTO {


    @NotNull(message = "订阅步骤详情不能为空")
    @Schema(description = "订阅步骤详情id，必填")
    private Long id;

    @NotNull(message = "订阅ID不能为空")
    @Schema(description = "订阅id")
    private Long subsId;

    @NotNull(message = "订阅结束时间不能为空")
    @Schema(description = "订阅结束时间")
    private Long endTime;

    @Schema(description = "免费试用天数")
    private Integer freeTryoutDays;
}