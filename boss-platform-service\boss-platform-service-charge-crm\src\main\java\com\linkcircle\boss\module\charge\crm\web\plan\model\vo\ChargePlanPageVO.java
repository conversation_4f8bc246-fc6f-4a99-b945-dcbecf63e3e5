package com.linkcircle.boss.module.charge.crm.web.plan.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "计划分页返回结果")
public class ChargePlanPageVO {

    @Schema(description = "计划ID")
    private Long id;

    @Schema(description = "计划名称")
    private String planName;

    @Schema(description = "货币代码")
    private String currencyCode;

    @Schema(description = "支付类型 0-预付费 1-后付费 字典：plan_payment_type")
    private Integer paymentType;

    @Schema(description = "状态 0-未激活 1-激活 2-存档 字典：plan_status")
    private Integer status;

    @Schema(description = "创建人")
    private String creator;

    @Schema(description = "创建时间")
    private Long createTime;

    @Schema(description = "描述")
    private String remark;
}