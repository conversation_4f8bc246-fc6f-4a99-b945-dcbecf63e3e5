package com.linkcircle.boss.module.charge.fee.web.bill.service.impl;

import com.linkcircle.boss.framework.common.exception.ServiceException;
import com.linkcircle.boss.framework.common.exception.util.ServiceExceptionUtil;
import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.module.charge.fee.constants.ErrorCodeConstants;
import com.linkcircle.boss.module.charge.fee.web.bill.mapper.MakeupBillMapper;
import com.linkcircle.boss.module.charge.fee.web.bill.mapper.PostpaidMapper;
import com.linkcircle.boss.module.charge.fee.web.bill.mapper.PrepaidMapper;
import com.linkcircle.boss.module.charge.fee.web.bill.service.BillIdGenerator;
import com.linkcircle.boss.module.charge.fee.web.bill.service.InvoiceIdGenerator;
import com.linkcircle.boss.module.charge.fee.web.bill.service.impl.generator.DefaultIdGenerator;
import com.linkcircle.boss.module.charge.fee.web.bill.service.impl.generator.NumberHandler;
import com.linkcircle.boss.module.charge.fee.web.bill.service.impl.generator.YearMonthDayHandler;
import com.linkcircle.boss.module.charge.fee.web.invoice.mapper.InvoiceMapper;
import com.linkcircle.boss.module.crm.api.basicConfig.BasicConfigApi;
import com.linkcircle.boss.module.crm.api.basicConfig.vo.InvoiceDetailsVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2025/7/23 16:39
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class InvoiceIdGeneratorImpl implements InvoiceIdGenerator {

    private final BasicConfigApi basicConfigApi;

    private final NumberHandler numberHandler;

    private final YearMonthDayHandler yearMonthDayHandler;

    private final DefaultIdGenerator defaultIdGenerator;


    private final InvoiceMapper invoiceMapper;

    private final PostpaidMapper postpaidMapper;

    private final PrepaidMapper prepaidMapper;

    private final MakeupBillMapper makeupBillMapper;

    /**
     * 生成唯一ID的方法
     *
     * @param entityId 实体ID
     * @return 生成的唯一ID字符串
     * @throws ServiceException 当账单发票号生成失败时抛出异常
     */
    @Override
    public String generateId(Long entityId) {
        CommonResult<InvoiceDetailsVO> commonResult = basicConfigApi.queryInvoice(entityId, 0);
        return generateIdInternal(commonResult,entityId, 0,null,null);
    }

    private String generateIdInternal( CommonResult<InvoiceDetailsVO> commonResult,Long entityId, int times,BiConsumer<InvoiceDetailsVO,String> process,String prefix) {
        String invoiceId = null;
        // 调用basicConfigApi的queryInvoice方法查询发票信息

        // 如果查询成功
        if (commonResult.isSuccess()) {
            // 如果查询结果不为空且NumberFormat不为空
            if (commonResult.getData() != null && StringUtils.isNotBlank(commonResult.getData().getNumberFormat())) {
                // 获取发票详情
                InvoiceDetailsVO config = commonResult.getData();
                // 处理前缀替换逻辑，如果有自定义前缀则替换原有前缀
                if(process!=null){
                    process.accept(config,prefix);
                }
                // 调用numberHandler的handle方法处理发票详情和年月日处理器，生成唯一ID
                invoiceId = yearMonthDayHandler.handle(config, numberHandler);
            } else {
                // 如果未找到规则，记录日志并采用默认规则生成BS+时间戳格式的ID
                log.info("账单发票号生成失败,未找到规则: {},采用默认规则生成BS+时间戳", entityId);
                invoiceId = defaultIdGenerator.generateId("BS");
            }
        } else {
            // 如果查询失败，记录日志并抛出异常
            log.info("账单发票号生成失败,未找到规则: {}", entityId);
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.BILL_INVOICE_ID_GENERATE_ERROR_NOT_FOUND_RULE, entityId);
        }
        if (StringUtils.isBlank(invoiceId)) {
            invoiceId = defaultIdGenerator.generateId("BS");
        }

        long count = invoiceMapper.checkInvoiceIdDuplicate(invoiceId);
        if (count > 0) {
            if(times<30){
                log.info("账单发票号生成失败,重复: {}", invoiceId);
                return generateIdInternal(commonResult,entityId, times + 1,process,prefix);
            }else{
                return  defaultIdGenerator.generateSnowId("BS");
            }
        }
        // 判断是否重复
        return invoiceId;
    }


    /**
     * 生成ID
     *
     * @param entityId 实体ID
     * @param prefix   前缀
     * @return 生成的ID
     * @throws ServiceException 如果未找到规则生成ID，则抛出异常
     */
    @Override
    public String generateId(Long entityId, String prefix) {
        BiConsumer<InvoiceDetailsVO,String> process = (config,newPrefix) -> {
            String numberFormat = config.getNumberFormat();
            String oldPrefix = extraPrefix(numberFormat);
            String newFormat =   newPrefix + numberFormat.substring(oldPrefix==null?0:oldPrefix.length());
            config.setNumberFormat(newFormat);
        };
        return generateIdInternal(basicConfigApi.queryInvoice(entityId, 0), entityId,0, process, prefix);
    }


    private static final Pattern PATTERN = Pattern.compile("^\\w+\\{");

    public static String extraPrefix(String numberFormat) {
        Matcher matcher = PATTERN.matcher(numberFormat);
        if (matcher.find()) {
            return matcher.group();
        }
        return null;
    }
}
