package com.linkcircle.boss.module.charge.crm.web.plan.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linkcircle.boss.framework.common.exception.ServiceException;
import com.linkcircle.boss.framework.common.model.PageResult;
import com.linkcircle.boss.framework.mybatis.core.util.MyBatisUtils;
import com.linkcircle.boss.framework.web.core.util.WebFrameworkUtils;
import com.linkcircle.boss.module.charge.crm.constants.ErrorCodeConstants;
import com.linkcircle.boss.module.charge.crm.web.coupon.model.entity.ChargeCouponDO;
import com.linkcircle.boss.module.charge.crm.web.coupon.service.ChargeCouponService;
import com.linkcircle.boss.module.charge.crm.web.plan.convert.*;
import com.linkcircle.boss.module.charge.crm.web.plan.mapper.ChargePlanMapper;
import com.linkcircle.boss.module.charge.crm.web.plan.model.dto.*;
import com.linkcircle.boss.module.charge.crm.web.plan.model.entity.ChargePlanDO;
import com.linkcircle.boss.module.charge.crm.web.plan.model.entity.ChargePlanProductDO;
import com.linkcircle.boss.module.charge.crm.web.plan.model.entity.ChargePlanProductServiceCouponDO;
import com.linkcircle.boss.module.charge.crm.web.plan.model.entity.ChargePlanProductServiceDO;
import com.linkcircle.boss.module.charge.crm.web.plan.model.vo.*;
import com.linkcircle.boss.module.charge.crm.web.plan.service.ChargePlanProductServeCouponService;
import com.linkcircle.boss.module.charge.crm.web.plan.service.ChargePlanProductServeService;
import com.linkcircle.boss.module.charge.crm.web.plan.service.ChargePlanProductService;
import com.linkcircle.boss.module.charge.crm.web.plan.service.ChargePlanService;
import com.linkcircle.boss.module.charge.crm.web.product.model.entity.ChargeProductDO;
import com.linkcircle.boss.module.charge.crm.web.product.service.IChargeProductService;
import com.linkcircle.boss.module.charge.crm.web.productservice.model.entity.ChargeProductServicePriceDO;
import com.linkcircle.boss.module.charge.crm.web.productservice.service.IChargeProductServicePriceService;
import com.linkcircle.boss.module.crm.api.common.dto.CommonDTO;
import com.linkcircle.boss.module.crm.api.common.vo.CommonVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 计划 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ChargePlanServiceImpl extends ServiceImpl<ChargePlanMapper, ChargePlanDO> implements ChargePlanService {

    private final ChargePlanProductService chargePlanProductService;

    private final ChargePlanProductServeService chargePlanProductServeService;

    private final ChargePlanProductServeCouponService chargePlanProductServeCouponService;

    private final ChargeCouponService chargeCouponService;

    private final IChargeProductServicePriceService chargeProductServicePriceService;

    private final IChargeProductService chargeProductService;

    /**
     * 分页查询计划
     *
     * @param dto 分页查询条件
     * @return 分页结果
     */
    @Override
    public PageResult<ChargePlanPageVO> page(ChargePlanPageReqDTO dto) {
        Page<?> page = MyBatisUtils.buildPage(dto);
        List<ChargePlanPageVO> list = baseMapper.queryByPage(page, dto);
        return MyBatisUtils.convert2PageResult(page, list);
    }


    @Override
    public List<ChargePlanDO> queryAll(ChargePlanSelectDTO dto) {
        // 根据支付类型和货币代码查询计划列表，如果值为空则不查询该字段
        return lambdaQuery()
                .eq(ObjectUtil.isNotEmpty(dto.getPaymentOptions()), ChargePlanDO::getPaymentType, dto.getPaymentOptions())
                .eq(ObjectUtil.isNotEmpty(dto.getCurrencyCode()), ChargePlanDO::getCurrencyCode, dto.getCurrencyCode())
                .list();
    }

    /**
     * 获取收费计划详情
     *
     * @param id 收费计划ID
     * @return 收费计划详情VO对象
     */
    @Override
    public ChargePlanDetailVO detail(Long id) {
        // 1. 查询基础数据
        List<ChargePlanProductDO> products = queryProducts(id);
        List<ChargePlanProductServiceDO> services = queryServices(id);
        List<ChargePlanProductServiceCouponDO> coupons = queryCoupons(id);

        // 2. 准备转换上下文
        PlanConversionContext context = new PlanConversionContext();
        context.setProductMap(getProductMap(products));
        context.setServiceMap(getServiceMap(services));
        context.setCouponMap(getCouponMap(coupons));

        // 3. 使用带上下文的转换
        List<ChargePlanProductDetailVO> productVOs = ChargePlanProductConvert.INSTANCE
                .dos2DetailVOSWithContext(products, context.getProductMap());

        List<ChargePlanProductServiceDetailVO> serviceVOs = ChargePlanProductServiceConvert.INSTANCE
                .dos2DetailVOSWithContext(services, context);

        List<ChargePlanProductServiceCouponDetailVO> couponVOs = ChargePlanProductServiceCouponConvert.INSTANCE
                .dos2DetailVOSWithContext(coupons, context);

        // 4. 构建关联关系
        buildRelationships(productVOs, serviceVOs, couponVOs);

        // 5. 返回结果
        ChargePlanDO planDO = getById(id);
        if (Objects.isNull(planDO)) {
            throw new ServiceException(ErrorCodeConstants.PLAN_NOT_EXISTS);
        }
        ChargePlanDetailVO result = ChargePlanConvert.INSTANCE.convert(planDO);
        result.setProducts(productVOs);
        return result;
    }

    /**
     * 根据提供的ChargePlanProductDO列表获取对应的ChargeProductDO映射
     *
     * @param products ChargePlanProductDO列表
     * @return ChargeProductDO的id为键，ChargeProductDO对象为值的映射
     */
    private Map<Long, ChargeProductDO> getProductMap(List<ChargePlanProductDO> products) {
        // 将products流中的每个元素转换为productId，并过滤掉null值，最后收集到Set中
        Set<Long> productIds = products.stream()
                .map(ChargePlanProductDO::getProductId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 如果productIds为空，则返回空的Map
        if (productIds.isEmpty()) {
            return Collections.emptyMap();
        }

        // 使用lambdaQuery查询chargeProductService中的记录
        return chargeProductService.lambdaQuery()
                // 查询条件：id在productIds中
                .in(ChargeProductDO::getId, productIds)
                // 获取查询结果列表
                .list()
                // 将查询结果流转换为Map，键为ChargeProductDO的id，值为ChargeProductDO对象本身
                .stream()
                .collect(Collectors.toMap(ChargeProductDO::getId, Function.identity()));
    }


    /**
     * 根据提供的服务列表，获取一个以服务ID为键，服务价格信息为值的映射。
     *
     * @param services 服务列表
     * @return 一个Map，键为服务ID，值为对应的服务价格信息对象
     */
    private Map<Long, ChargeProductServicePriceDO> getServiceMap(List<ChargePlanProductServiceDO> services) {
        // 获取服务ID的集合
        Set<Long> serviceIds = services.stream()
                .map(ChargePlanProductServiceDO::getServiceId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 如果服务ID集合为空，则返回空Map
        if (serviceIds.isEmpty()) {
            return Collections.emptyMap();
        }

        // 查询对应的服务价格信息
        return chargeProductServicePriceService.lambdaQuery()
                .in(ChargeProductServicePriceDO::getId, serviceIds)
                .list()
                .stream()
                .collect(Collectors.toMap(ChargeProductServicePriceDO::getId, Function.identity()));
    }


    /**
     * 从优惠券列表中获取优惠券ID对应的ChargeCouponDO对象的Map。
     *
     * @param coupons 优惠券列表
     * @return 包含优惠券ID和对应的ChargeCouponDO对象的Map，如果优惠券列表为空，则返回空的Map
     */
    private Map<Long, ChargeCouponDO> getCouponMap(List<ChargePlanProductServiceCouponDO> coupons) {
        // 从优惠券列表中获取所有非空的优惠券ID
        Set<Long> couponIds = coupons.stream()
                .map(ChargePlanProductServiceCouponDO::getCouponId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 如果优惠券ID集合为空，则返回空的Map
        if (couponIds.isEmpty()) {
            return Collections.emptyMap();
        }

        // 通过优惠券服务查询符合条件的优惠券列表
        return chargeCouponService.lambdaQuery()
                .in(ChargeCouponDO::getId, couponIds)
                .list()
                .stream()
                // 将查询结果转换为Map，键为优惠券ID，值为ChargeCouponDO对象
                .collect(Collectors.toMap(ChargeCouponDO::getId, Function.identity()));
    }


    /**
     * 建立产品、服务和优惠券之间的关系
     *
     * @param products 产品列表
     * @param services 服务列表
     * @param coupons  优惠券列表
     */
    private void buildRelationships(List<ChargePlanProductDetailVO> products,
                                    List<ChargePlanProductServiceDetailVO> services,
                                    List<ChargePlanProductServiceCouponDetailVO> coupons) {
        // 将服务按产品ID分组
        Map<Long, List<ChargePlanProductServiceDetailVO>> serviceMap = services.stream()
                .collect(Collectors.groupingBy(ChargePlanProductServiceDetailVO::getPlanProductId));

        // 将优惠券按服务ID分组
        Map<Long, List<ChargePlanProductServiceCouponDetailVO>> couponMap = coupons.stream()
                .collect(Collectors.groupingBy(ChargePlanProductServiceCouponDetailVO::getPlanProductServiceId));

        // 为每个服务设置对应的优惠券列表
        services.forEach(s -> s.setCoupons(couponMap.getOrDefault(s.getId(), Collections.emptyList())));

        // 为每个产品设置对应的服务列表
        products.forEach(p -> p.setServices(serviceMap.getOrDefault(p.getId(), Collections.emptyList())));
    }


    /**
     * 根据计划ID查询产品列表
     *
     * @param planId 计划ID
     * @return 产品列表
     */
    private List<ChargePlanProductDO> queryProducts(Long planId) {
        return chargePlanProductService.lambdaQuery()
                .eq(ChargePlanProductDO::getPlanId, planId)
                .eq(ChargePlanProductDO::getDeleted, 0)
                .list();
    }

    /**
     * 根据计划ID查询对应的服务列表
     *
     * @param planId 计划ID
     * @return 返回对应的服务列表
     */
    private List<ChargePlanProductServiceDO> queryServices(Long planId) {
        return chargePlanProductServeService.lambdaQuery()
                .eq(ChargePlanProductServiceDO::getPlanId, planId)
                .eq(ChargePlanProductServiceDO::getDeleted, 0)
                .list();
    }

    /**
     * 查询指定计划ID下的优惠券列表
     *
     * @param planId 计划ID
     * @return 优惠券列表
     */
    private List<ChargePlanProductServiceCouponDO> queryCoupons(Long planId) {
        return chargePlanProductServeCouponService.lambdaQuery()
                .eq(ChargePlanProductServiceCouponDO::getPlanId, planId)
                .eq(ChargePlanProductServiceCouponDO::getDeleted, 0)
                .orderByAsc(ChargePlanProductServiceCouponDO::getSequence)
                .list();
    }

    /**
     * 创建计费计划
     *
     * @param dto 创建计费计划的请求DTO
     * @return 创建的计费计划ID
     * @throws ServiceException 如果dto为空或dto中的产品列表为空，则抛出ServiceException异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long create(ChargePlanCreateReqDTO dto) {
        // 参数校验
        // 检查dto是否为空或dto中的产品列表是否为空
        if (ObjectUtil.isEmpty(dto)) {
            throw new ServiceException(ErrorCodeConstants.PLAN_NOT_EXISTS);
        }

        // 主计划创建
        // 生成主计划的ID
        long planId = IdUtil.getSnowflakeNextId();
        // 将dto转换为ChargePlanDO对象
        ChargePlanDO chargePlanDO = ChargePlanConvert.INSTANCE.convert(dto).setId(planId);
        // 创建默认状态为未激活
        chargePlanDO.setStatus(0);
        // 保存主计划
        save(chargePlanDO);

        // 创建产品列表、服务列表和优惠券列表
        List<ChargePlanProductDO> productList = new ArrayList<>();
        List<ChargePlanProductServiceDO> serviceList = new ArrayList<>();
        List<ChargePlanProductServiceCouponDO> couponList = new ArrayList<>();
        // 产品必填
        if (CollectionUtils.isEmpty(dto.getProducts()))
            throw new ServiceException(ErrorCodeConstants.PLAN_PRODUCT_CANNOT_BE_EMPTY);
        // 遍历dto中的产品列表
        for (ChargePlanProductCreateReqDTO product : dto.getProducts()) {
            // 生成产品ID
            long planProductId = IdUtil.getSnowflakeNextId();
            // 创建产品DO对象
            ChargePlanProductDO productDO = ChargePlanProductDO.builder()
                    .id(planProductId)
                    .planId(planId)
                    .productId(product.getProductId())
                    .build();
            // 将产品DO对象添加到产品列表中
            productList.add(productDO);

            // 如果产品中的服务列表为空，则继续处理下一个产品对象（即跳过当前产品的后续操作）
            if (CollectionUtils.isEmpty(product.getServices())) continue;
            // 遍历产品中的服务列表
            for (ChargePlanProductServiceCreateReqDTO service : product.getServices()) {
                // 生成服务ID
                long planProductServiceId = IdUtil.getSnowflakeNextId();
                // 创建服务DO对象
                ChargePlanProductServiceDO serviceDO = ChargePlanProductServiceDO.builder()
                        .id(planProductServiceId)
                        .planId(planId)
                        .planProductId(planProductId)
                        .serviceId(service.getServiceId())
                        .externalName(service.getExternalName())
                        .externalDescription(service.getExternalDescription())
                        .build();
                // 将服务DO对象添加到服务列表中
                serviceList.add(serviceDO);

                // 将服务中的优惠券列表转换为优惠券DO对象列表
                List<ChargePlanProductServiceCouponDO> coupons = ChargePlanProductServiceCouponConvert.INSTANCE.convertList(service.getCoupons());
                // 如果优惠券列表为空，则继续处理下一个服务
                if (CollectionUtils.isEmpty(coupons)) continue;
                // 遍历优惠券列表
                for (ChargePlanProductServiceCouponDO coupon : coupons) {
                    // 设置优惠券的服务ID和计划ID
                    coupon.setPlanProductServiceId(planProductServiceId);
                    coupon.setPlanId(planId);
                    coupon.setId(IdUtil.getSnowflakeNextId());
                    // 将优惠券DO对象添加到优惠券列表中
                    couponList.add(coupon);
                }
            }
        }

        // 批量保存产品、服务和优惠券
        // 批量保存产品列表
        chargePlanProductService.saveBatch(productList);
        // 批量保存服务列表
        chargePlanProductServeService.saveBatch(serviceList);
        // 批量保存优惠券列表
        chargePlanProductServeCouponService.saveBatch(couponList);

        // 返回主计划的ID
        return planId;
    }


    /**
     * 更新收费计划
     *
     * @param dto 收费计划创建请求DTO
     * @return 更新成功返回true，否则返回false
     * @throws ServiceException 当参数无效或收费计划不存在时抛出异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(ChargePlanCreateReqDTO dto) {
        // 1. 参数校验
        // 检查dto是否为null或dto的id是否为null
        if (dto == null || dto.getId() == null) {
            throw new ServiceException(ErrorCodeConstants.PLAN_NOT_EXISTS);
        }

        Long planId = dto.getId();

        // 2. 更新主计划信息
        // 将dto转换为ChargePlanDO对象
        ChargePlanDO chargePlanDO = ChargePlanConvert.INSTANCE.convert(dto);
        String loginUsername = WebFrameworkUtils.getLoginUsername();
        long nowTime = System.currentTimeMillis();
        chargePlanDO.setUpdater(loginUsername);
        chargePlanDO.setUpdateTime(nowTime);
        // 更新数据库中的ChargePlanDO对象
        if (!updateById(chargePlanDO)) {
            throw new ServiceException(ErrorCodeConstants.PLAN_NOT_EXISTS);
        }

        List<ChargePlanProductDO> productList = new ArrayList<>();
        List<ChargePlanProductServiceDO> serviceList = new ArrayList<>();
        List<ChargePlanProductServiceCouponDO> couponList = new ArrayList<>();

        // 3. 构建新的关联数据
        // 遍历dto中的产品列表
        for (ChargePlanProductCreateReqDTO product : dto.getProducts()) {
            long planProductId = IdUtil.getSnowflakeNextId();
            // 构建ChargePlanProductDO对象并添加到productList中
            ChargePlanProductDO productDO = ChargePlanProductDO.builder()
                    .id(planProductId)
                    .planId(planId)
                    .productId(product.getProductId())
                    .build();
            productDO.setCreator(loginUsername);
            productDO.setCreateTime(nowTime);
            productList.add(productDO);

            // 遍历产品中的服务列表
            for (ChargePlanProductServiceCreateReqDTO service : product.getServices()) {
                long planProductServiceId = IdUtil.getSnowflakeNextId();
                // 构建ChargePlanProductServiceDO对象并添加到serviceList中
                ChargePlanProductServiceDO serviceDO = ChargePlanProductServiceDO.builder()
                        .id(planProductServiceId)
                        .planId(planId)
                        .planProductId(planProductId)
                        .serviceId(service.getServiceId())
                        .externalName(service.getExternalName())
                        .externalDescription(service.getExternalDescription())
                        .build();
                serviceDO.setCreator(loginUsername);
                serviceDO.setCreateTime(nowTime);
                serviceList.add(serviceDO);

                // 将服务中的优惠券列表转换为ChargePlanProductServiceCouponDO对象列表
                List<ChargePlanProductServiceCouponDO> coupons = ChargePlanProductServiceCouponConvert.INSTANCE.convertList(service.getCoupons());
                // 遍历优惠券列表
                for (ChargePlanProductServiceCouponDO coupon : coupons) {
                    // 设置优惠券的关联信息
                    coupon.setPlanProductServiceId(planProductServiceId);
                    coupon.setPlanId(planId);
                    coupon.setId(IdUtil.getSnowflakeNextId());
                    coupon.setCreator(loginUsername);
                    coupon.setCreateTime(nowTime);
                    // 将优惠券添加到couponList中
                    couponList.add(coupon);
                }
            }
        }

        // 4. 删除旧的关联数据
        // 删除与指定计划ID关联的产品信息
        chargePlanProductService.remove(new LambdaQueryWrapper<ChargePlanProductDO>()
                .eq(ChargePlanProductDO::getPlanId, planId));
        // 删除与指定计划ID关联的产品服务信息
        chargePlanProductServeService.remove(new LambdaQueryWrapper<ChargePlanProductServiceDO>()
                .eq(ChargePlanProductServiceDO::getPlanId, planId));
        // 删除与指定计划ID关联的产品服务优惠券信息
        chargePlanProductServeCouponService.remove(new LambdaQueryWrapper<ChargePlanProductServiceCouponDO>()
                .eq(ChargePlanProductServiceCouponDO::getPlanId, planId));

        // 5. 批量插入新的关联数据
        // 批量插入新的产品信息
        chargePlanProductService.saveBatch(productList);
        // 批量插入新的产品服务信息
        chargePlanProductServeService.saveBatch(serviceList);
        // 批量插入新的产品服务优惠券信息
        chargePlanProductServeCouponService.saveBatch(couponList);

        return true;
    }


    /**
     * 更新充值计划的状态
     *
     * @param id     充值计划的ID
     * @param status 新的状态
     * @return 如果更新成功返回true，否则返回false
     */
    @Override
    public boolean changeStatus(Long id, Integer status) {
        return lambdaUpdate().eq(ChargePlanDO::getId, id).set(ChargePlanDO::getStatus, status).update();
    }

    /**
     * 根据ID删除记录
     *
     * @param id 要删除记录的ID
     * @return 如果删除成功，则返回true；否则返回false
     */
    @Override
    public boolean delete(Long id) {
        return removeById(id);
    }

    /**
     * 复制套餐计划
     *
     * @param id 套餐计划的ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void copy(Long id) {
        long nowTime = System.currentTimeMillis();
        String loginUsername = WebFrameworkUtils.getLoginUsername();
        // 根据ID查询要复制的套餐计划
        ChargePlanDO plan = lambdaQuery().eq(ChargePlanDO::getId, id).one();
        // 生成新的套餐计划名称
        String newName = generateNestedCopyName(plan.getPlanName());
        // 生成新的套餐计划ID
        long newPlanId = IdUtil.getSnowflakeNextId();
        // 保存旧的套餐计划ID
        Long oldPlanId = plan.getId();
        // 更新套餐计划的ID和名称
        plan.setId(newPlanId);
        plan.setPlanName(newName);
        plan.setCreateTime(nowTime);
        plan.setUpdateTime(nowTime);
        plan.setCreator(loginUsername);
        plan.setUpdater(loginUsername);
        // 查询旧的套餐产品列表
        List<ChargePlanProductDO> productDOS = chargePlanProductService
                .lambdaQuery()
                .eq(ChargePlanProductDO::getPlanId, oldPlanId)
                .list();

        // 查询旧的套餐服务列表
        List<ChargePlanProductServiceDO> serviceDOS = chargePlanProductServeService
                .lambdaQuery()
                .eq(ChargePlanProductServiceDO::getPlanId, oldPlanId)
                .list();

        // 查询旧的套餐优惠券列表
        List<ChargePlanProductServiceCouponDO> couponDOS = chargePlanProductServeCouponService
                .lambdaQuery()
                .eq(ChargePlanProductServiceCouponDO::getPlanId, oldPlanId)
                .list();

        // 旧产品id和新产品的映射关系，用于更新服务中的planProductId
        HashMap<Long, Long> planProductIdsMap = new HashMap<>();
        // 旧服务id和新服务的映射关系，用于更新优惠券中的planProductServiceId
        HashMap<Long, Long> productServiceIdsMap = new HashMap<>();

        // 遍历旧的套餐产品列表，生成新的产品ID，并更新映射关系
        for (ChargePlanProductDO productDO : productDOS) {
            Long newPlanProductId = IdUtil.getSnowflakeNextId();
            Long oldPlanProductId = productDO.getId();
            productDO.setId(newPlanProductId);
            productDO.setPlanId(newPlanId);
            productDO.setCreateTime(nowTime);
            productDO.setUpdateTime(nowTime);
            productDO.setCreator(loginUsername);
            productDO.setUpdater(loginUsername);
            planProductIdsMap.put(oldPlanProductId, newPlanProductId);
        }

        // 遍历旧的套餐服务列表，生成新的服务ID，并更新映射关系
        for (ChargePlanProductServiceDO serviceDO : serviceDOS) {
            Long newProductServiceId = IdUtil.getSnowflakeNextId();
            Long oldProductServiceId = serviceDO.getId();
            serviceDO.setId(newProductServiceId);
            serviceDO.setPlanId(newPlanId);
            serviceDO.setCreateTime(nowTime);
            serviceDO.setUpdateTime(nowTime);
            serviceDO.setCreator(loginUsername);
            serviceDO.setUpdater(loginUsername);
            serviceDO.setPlanProductId(planProductIdsMap.get(serviceDO.getPlanProductId()));
            productServiceIdsMap.put(oldProductServiceId, newProductServiceId);
        }

        // 遍历旧的套餐优惠券列表，更新新的套餐计划ID和服务ID
        for (ChargePlanProductServiceCouponDO couponDO : couponDOS) {
            couponDO.setId(IdUtil.getSnowflakeNextId());
            couponDO.setPlanId(newPlanId);
            couponDO.setCreateTime(nowTime);
            couponDO.setUpdateTime(nowTime);
            couponDO.setCreator(loginUsername);
            couponDO.setUpdater(loginUsername);
            couponDO.setPlanProductServiceId(productServiceIdsMap.get(couponDO.getPlanProductServiceId()));
        }

        // 保存新的套餐计划
        save(plan);
        // 批量保存新的套餐产品列表
        chargePlanProductService.saveBatch(productDOS);
        // 批量保存新的套餐服务列表
        chargePlanProductServeService.saveBatch(serviceDOS);
        // 批量保存新的套餐优惠券列表
        chargePlanProductServeCouponService.saveBatch(couponDOS);
    }

    @Override
    public List<CommonVO> findNameByIds(CommonDTO commonDTO) {
        QueryWrapper<ChargePlanDO> wrapper = new QueryWrapper<>();
        wrapper.select("id", "plan_name");
        wrapper.in("id", commonDTO.getIds());
        List<ChargePlanDO> plans = list(wrapper);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(plans)) {
            return plans.stream().map(t -> CommonVO.builder().id(t.getId()).name(t.getPlanName()).build()).toList();
        }
        return List.of();
    }


    /**
     * 生成嵌套式复制名称
     * <p>
     * 该方法用于生成一个新的唯一名称，方法基于原始名称并在其后追加"_copy"和数字编号。
     * 如果生成的名称已经存在于数据库中，则会递增编号直到生成一个唯一的名称。
     * 如果最终生成的名称长度超过64个字符，则会抛出ServiceException异常。
     *
     * @param originalName 原始名称，类型为String
     * @return 返回一个新的唯一名称，类型为String
     * @throws ServiceException 如果最终生成的名称长度超过64个字符，则抛出该异常
     */
    public String generateNestedCopyName(String originalName) {
        // 直接在原名称后追加_copy1
        // 初始化基础名称和复制编号
        String baseName = originalName;
        int copyNumber = 1;
        String newName = baseName + "_copy" + copyNumber;

        // 检查是否存在，如果存在则递增数字
        // 循环检查新名称是否存在于数据库中
        while (doesNameExistInDatabase(newName)) {
            // 递增复制编号
            copyNumber++;
            // 更新新名称为基础名称加递增后的复制编号
            newName = baseName + "_copy" + copyNumber;
        }

        // 检查新名称长度是否超过64个字符
        if (newName.length() > 64) {
            // 如果长度超过64个字符，则抛出ServiceException异常
            throw new ServiceException(ErrorCodeConstants.NAME_TOO_LONG);
        }

        // 返回新名称
        return newName;
    }


    /**
     * 检查名称是否已存在于数据库中
     *
     * @param name 需要检查的名称
     * @return 如果名称已存在于数据库中，则返回 true；否则返回 false
     */
    private boolean doesNameExistInDatabase(String name) {
        // 查询数据库以检查名称是否存在
        // 返回 true 如果名称已存在，false 如果不存在
        return lambdaQuery().eq(ChargePlanDO::getPlanName, name).exists();
    }

}