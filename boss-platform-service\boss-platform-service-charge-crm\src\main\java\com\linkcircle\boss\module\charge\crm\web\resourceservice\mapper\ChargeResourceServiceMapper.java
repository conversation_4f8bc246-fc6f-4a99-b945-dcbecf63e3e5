package com.linkcircle.boss.module.charge.crm.web.resourceservice.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linkcircle.boss.module.charge.crm.web.resource.model.dto.VersionInfoListParam;
import com.linkcircle.boss.module.charge.crm.web.resourceservice.model.dto.ChargeResourceServiceQueryDTO;
import com.linkcircle.boss.module.charge.crm.web.resourceservice.model.entity.ChargeResourceService;
import com.linkcircle.boss.module.charge.crm.web.resourceservice.model.vo.ChargeResourceServiceVO;
import com.linkcircle.boss.module.charge.crm.web.resourceservice.model.vo.ChargeResourceServiceVersionInfoVO;
import com.linkcircle.boss.module.charge.crm.web.resourceservice.model.vo.ChargeResourceServiceVersionVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 资源服务基本信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
@Mapper
public interface ChargeResourceServiceMapper extends BaseMapper<ChargeResourceService> {

    List<ChargeResourceServiceVO> pageQuery(@Param("page") Page<?> page, @Param("query") ChargeResourceServiceQueryDTO queryDTO);

    List<ChargeResourceServiceVersionVO> getVersionList(Long serviceId);

    List<ChargeResourceServiceVersionInfoVO> getVersionInfoById(@Param("versionId") Long versionId, @Param("idList") List<Long> idList);

    ChargeResourceServiceVersionInfoVO getMaxVersionInfoById(Long serviceId);

    List<ChargeResourceServiceVersionInfoVO> getVersionInfoListByParam(@Param("param") VersionInfoListParam param);

    void removeNullVersionService(@Param("idList") List<Long> versionIds);
}
