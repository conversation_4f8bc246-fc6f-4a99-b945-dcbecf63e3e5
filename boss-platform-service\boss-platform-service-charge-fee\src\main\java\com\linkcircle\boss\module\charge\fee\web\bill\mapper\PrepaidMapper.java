package com.linkcircle.boss.module.charge.fee.web.bill.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.linkcircle.boss.module.billing.api.bill.product.model.entity.PostpaidProductIncomeBillDO;
import com.linkcircle.boss.module.billing.api.detail.income.model.entity.PrepaidIncomeBillDetailDO;
import com.linkcircle.boss.module.charge.fee.enums.BillEnum;
import com.linkcircle.boss.module.charge.fee.enums.InvoiceEnum;
import com.linkcircle.boss.module.charge.fee.web.bill.model.entity.MakeupIncomeBillDO;
import com.linkcircle.boss.module.charge.fee.web.invoice.model.dto.BillDetailReqDTO;
import com.linkcircle.boss.module.charge.fee.web.invoice.model.dto.BillInvoiceCheckGroupDTO;
import com.linkcircle.boss.module.charge.fee.web.invoice.model.dto.BillInvoiceIdDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @date 2025/6/25 14:50
 */
@Mapper
public interface PrepaidMapper extends BaseMapper<PrepaidIncomeBillDetailDO> {

    public List<BillInvoiceCheckGroupDTO> checkByIds(@Param("detailIds") List<Long> detailIds);

    public List<BillInvoiceCheckGroupDTO> checkByDetail(@Param("req") BillDetailReqDTO reqDTO);

    PrepaidIncomeBillDetailDO queryById(Long billId);

    default boolean checkBillCodeDuplicate(String billCode) {
        LambdaQueryWrapper<PrepaidIncomeBillDetailDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PrepaidIncomeBillDetailDO::getBillNo, billCode);
        return selectCount(wrapper) > 0;
    }

    /**
     * 统计指定客户的未支付账单数量
     *
     * @param customerId 客户ID
     * @return 未支付账单数量
     */
    default long countUnPaidBill(Long customerId) {
        LambdaQueryWrapper<PrepaidIncomeBillDetailDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PrepaidIncomeBillDetailDO::getCustomerId, customerId);
        wrapper.eq(PrepaidIncomeBillDetailDO::getDeleted, false);
        wrapper.ne(PrepaidIncomeBillDetailDO::getBillStatus, InvoiceEnum.BillStatus.PAID.getCode());
        return selectCount(wrapper);
    }

    default BigDecimal calculateBillFee(Long constructId, Long startTime, Long endTime) {
        QueryWrapper<PrepaidIncomeBillDetailDO> wrapper = new QueryWrapper<>();
        wrapper.select("sum(amountWithTax) as billFee")
                .eq("contract_id", constructId)
                .eq("deleted", false);
        if(startTime != null) {
            wrapper.ge("billing_time", startTime);
        }
        if(endTime != null) {
            wrapper.le("billing_time", endTime);
        }
        wrapper.groupBy("contract_id");
        List<Map<String, Object>> maps = selectMaps(wrapper);
        BigDecimal billFee = null;
        if(!maps.isEmpty()) {
            for (Map<String, Object> map : maps) {
                billFee = (BigDecimal) map.get("billFee");
            }
        }
        return billFee==null?BigDecimal.ZERO:billFee;
    }

    default  int refundInvoice(Long billId, Long billingTime, BigDecimal finalRefundAmount){
        LambdaUpdateWrapper<PrepaidIncomeBillDetailDO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(PrepaidIncomeBillDetailDO::getBillDetailId,billId)
                .eq(PrepaidIncomeBillDetailDO::getBillingTime,billingTime);
        wrapper.set(PrepaidIncomeBillDetailDO::getRefundInvoiceAmount,finalRefundAmount);
        return update(wrapper);
    }
}
