package com.linkcircle.boss.module.charge.fee.web.bill.service.impl.generator;

import cn.hutool.core.util.IdUtil;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/7/24 9:34
 */
@Component
public class DefaultIdGenerator {

    /**
     * 生成带有前缀的唯一ID。
     *
     * @param prefix ID的前缀字符串
     * @return 带有前缀的唯一ID字符串
     */
    public synchronized String generateId(String prefix) {
        return prefix + System.nanoTime();
    }

    /**
     * 生成带有前缀的雪花算法唯一ID
     *
     * @param prefix ID前缀
     * @return 带有前缀的唯一ID字符串
     */
    public  String generateSnowId(String prefix) {
        return prefix + IdUtil.getSnowflakeNextId();
    }
}
