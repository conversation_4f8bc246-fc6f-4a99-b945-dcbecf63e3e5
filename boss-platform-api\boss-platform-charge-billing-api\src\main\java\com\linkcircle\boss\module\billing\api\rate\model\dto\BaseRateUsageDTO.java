package com.linkcircle.boss.module.billing.api.rate.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-07-09 9:44
 * @description 费率消耗量
 */
@Data
public class BaseRateUsageDTO {

    @Schema(description = "消耗量")
    private BigDecimal usage;

    @Schema(description = "单位")
    private String usageUnit;

    @Schema(description = "目录单价")
    private BigDecimal originalUnitPrice;

    @Schema(description = "目录总价(单价原价)")
    private BigDecimal originalPrice;

    @Schema(description = "订阅单价")
    private BigDecimal discountedUnitPrice;

    @Schema(description = "订阅总价(优惠的目录价)")
    private BigDecimal discountedPrice;

    /**
     * 计费单位数
     */
    private BigDecimal chargeUnitCount;

    /**
     * 税率
     */
    private BigDecimal taxRate;

    /**
     * 消耗量是否套餐内
     */
    private Boolean inPackage;

    /**
     * 是否命中
     */
    private Boolean isHit;

}
