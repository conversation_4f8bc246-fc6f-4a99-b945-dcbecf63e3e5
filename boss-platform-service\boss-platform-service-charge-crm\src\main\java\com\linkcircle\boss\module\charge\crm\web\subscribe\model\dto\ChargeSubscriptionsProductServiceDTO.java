package com.linkcircle.boss.module.charge.crm.web.subscribe.model.dto;

import com.linkcircle.boss.framework.tanant.TenantBaseDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 订阅服务表实体类
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChargeSubscriptionsProductServiceDTO extends TenantBaseDO {
    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "订阅id")
    private Long subsId;

    @Schema(description = "订阅产品关系表id")
    private Long subsProductId;

    @Schema(description = "服务id")
    private Long serviceId;

    @Schema(description = "服务名称")
    private String serviceName;

    @Schema(description = "目录价格配置")
    private String currencyPriceJson;

    @Schema(description = "是否含套餐外,0:不包含，1：包含(阶梯型), 2: 包含(固定型)")
    private Integer inPackage;

    @Schema(description = "间隔时长单位")
    private Integer period;

    @Schema(description = "间隔时长,0:日，1：星期，2：月，3：季度，4：一次性")
    private Integer unitPeriod;

    @Schema(description = "0:固定费率，1：阶梯费率，2：套餐计费，3：按量计费")
    private Integer chargeType;

    @Schema(description = "支付方式，0:现金，1：积分")
    private Integer paymentOptions;

    @Schema(description = "服务描述")
    private String description;

    @Schema(description = "单位标签")
    private String unitLabel;

    @Schema(description = "购买数量")
    private Long amount;

    @Schema(description = "服务编码")
    private String serviceCode;

    @Schema(description = "量表id")
    private Long scaleId;

    @Schema(description = "服务优惠列表")
    private List<ChargeSubscriptionsServiceCouponDTO> coupons;
}