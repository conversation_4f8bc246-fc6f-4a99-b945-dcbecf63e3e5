package com.linkcircle.boss.billing.sdk.example;

import com.linkcircle.boss.billing.sdk.BillingClient;
import com.linkcircle.boss.billing.sdk.BillingSDK;
import com.linkcircle.boss.billing.sdk.config.BillingConfig;
import com.linkcircle.boss.billing.sdk.exception.BillingException;
import com.linkcircle.boss.billing.sdk.model.request.CostBillDetailRequest;
import com.linkcircle.boss.billing.sdk.model.request.IncomeBillDetailRequest;
import com.linkcircle.boss.billing.sdk.model.response.CostBillDetailResponse;
import com.linkcircle.boss.billing.sdk.model.response.IncomeBillDetailResponse;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-07-25 15:00
 * @description 快速开始示例
 */
public class QuickStartExample {

    public static void main(String[] args) {
        // 示例1：简单创建客户端
        simpleExample();

        // 示例2：高级配置
        advancedExample();

        // 示例3：错误处理
        errorHandlingExample();
    }

    /**
     * 简单示例
     */
    public static void simpleExample() {
        System.out.println("=== 简单示例 ===");

        // 1. 创建客户端
        BillingClient client = BillingSDK.createClient(
                "http://localhost:49086",  // 计费平台地址
                "your_app_id",             // 应用ID
                "your_app_secret",         // 应用密钥
                1L                         // 租户ID
        );

        try {
            // 2. 构建收入账单明细请求
            IncomeBillDetailRequest incomeRequest = BillingSDK.incomeRequest()
                    .accountId(123456L)
                    .productId(789L)
                    .serviceId(456L)
                    .businessTime(System.currentTimeMillis())
                    .usage(100.5)
                    .usageUnit("minutes")
                    .callbackUrl("https://your-domain.com/callback")
                    .businessId("voice_call_" + System.currentTimeMillis())
                    .addData("call_duration", 100.5)
                    .addData("call_type", "voice")
                    .addData("from_number", "***********")
                    .addData("to_number", "***********")
                    .build();

            // 3. 推送收入账单明细
            IncomeBillDetailResponse incomeResponse = client.pushIncomeBillDetail(incomeRequest);
            System.out.println("收入账单推送成功, 账单ID: " + incomeResponse.getBillDetailId());

            // 4. 构建成本账单明细请求
            CostBillDetailRequest costRequest = BillingSDK.costRequest()
                    .accountId(123456L)
                    .resourceId(789L)
                    .resourceServiceId(456L)
                    .businessTime(System.currentTimeMillis())
                    .usage(50.0)
                    .usageUnit("GB")
                    .callbackUrl("https://your-domain.com/callback")
                    .businessId("bandwidth_" + System.currentTimeMillis())
                    .addData("bandwidth", 50.0)
                    .addData("region", "cn-beijing")
                    .build();

            // 5. 推送成本账单明细
            CostBillDetailResponse costResponse = client.pushCostBillDetail(costRequest);
            System.out.println("成本账单推送成功, 账单ID: " + costResponse.getBillDetailId());

        } catch (BillingException e) {
            System.err.println("推送失败: " + e.getMessage());
        } finally {
            // 6. 关闭客户端
            client.close();
        }
    }

    /**
     * 高级配置示例
     */
    public static void advancedExample() {
        System.out.println("\n=== 高级配置示例 ===");

        // 1. 创建高级配置
        BillingConfig config = BillingConfig.builder()
                .baseUrl("http://localhost:49086")
                .appId("your_app_id")
                .appSecret("your_app_secret")
                .tenantId(1L)
                .connectTimeout(30000)    // 连接超时30秒
                .readTimeout(60000)       // 读取超时60秒
                .writeTimeout(60000)      // 写入超时60秒
                .maxRetries(3)            // 最大重试3次
                .debug(true)              // 开启调试模式
                .build();

        // 2. 创建客户端
        BillingClient client = BillingSDK.createClient(config);

        try {
            // 3. 使用BigDecimal精确计算
            IncomeBillDetailRequest request = BillingSDK.incomeRequest()
                    .accountId(123456L)
                    .productId(789L)
                    .serviceId(456L)
                    .businessTime(System.currentTimeMillis())
                    .usage(new BigDecimal("100.123456"))  // 精确小数
                    .usageUnit("seconds")
                    .callbackUrl("https://your-domain.com/callback")
                    .businessId("precise_" + System.currentTimeMillis())
                    .addData("precise_usage", new BigDecimal("100.123456"))
                    .build();

            IncomeBillDetailResponse response = client.pushIncomeBillDetail(request);
            System.out.println("精确计算推送成功, 账单ID: " + response.getBillDetailId());

        } catch (BillingException e) {
            System.err.println("推送失败: " + e.getMessage());
        } finally {
            client.close();
        }
    }

    /**
     * 错误处理示例
     */
    public static void errorHandlingExample() {
        System.out.println("\n=== 错误处理示例 ===");

        BillingClient client = BillingSDK.createClient(
                "http://localhost:49086",
                "your_app_id",
                "your_app_secret",
                1L
        );

        try {
            // 故意构建一个缺少必要参数的请求
            IncomeBillDetailRequest request = BillingSDK.incomeRequest()
                    .accountId(123456L)
                    // 缺少productId和serviceId
                    .businessTime(System.currentTimeMillis())
                    .usage(100.0)
                    .usageUnit("minutes")
                    .callbackUrl("https://your-domain.com/callback")
                    .businessId("error_test_" + System.currentTimeMillis())
                    .build();

            client.pushIncomeBillDetail(request);

        } catch (BillingException e) {
            System.err.println("捕获到计费异常:");
            System.err.println("错误码: " + e.getErrorCode());
            System.err.println("错误信息: " + e.getMessage());

            // 根据错误码进行不同处理
            switch (e.getErrorCode()) {
                case "PARAM_ERROR":
                    System.err.println("参数错误，请检查请求参数");
                    break;
                case "HTTP_ERROR":
                    System.err.println("网络错误，请检查网络连接");
                    break;
                case "API_ERROR":
                    System.err.println("API错误，请检查服务状态");
                    break;
                default:
                    System.err.println("未知错误");
                    break;
            }
        } finally {
            client.close();
        }
    }
}
