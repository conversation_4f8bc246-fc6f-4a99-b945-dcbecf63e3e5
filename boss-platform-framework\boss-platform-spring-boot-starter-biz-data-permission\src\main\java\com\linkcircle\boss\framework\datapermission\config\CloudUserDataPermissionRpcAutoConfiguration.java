package com.linkcircle.boss.framework.datapermission.config;

import com.linkcircle.boss.framework.datapermission.core.rpc.UserDataPermissionRequestInterceptor;
import com.linkcircle.boss.framework.datapermission.core.rpc.UserDataPermissionRpcWebFilter;
import com.linkcircle.boss.module.system.api.permission.PermissionApi;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;

import static com.linkcircle.boss.framework.common.enums.WebFilterOrderEnum.USER_DATA_PERMISSION_FILTER;

/**
 * 数据权限针对 RPC 的自动配置类
 *
 * <AUTHOR>
 */
@EnableFeignClients(clients = {PermissionApi.class})
@AutoConfiguration
@ConditionalOnClass(name = "feign.RequestInterceptor")
public class CloudUserDataPermissionRpcAutoConfiguration {

    @Bean
    public UserDataPermissionRequestInterceptor userDataPermissionRequestInterceptor() {
        return new UserDataPermissionRequestInterceptor();
    }

    @Bean
    public FilterRegistrationBean<UserDataPermissionRpcWebFilter> userDataPermissionRpcFilter() {
        FilterRegistrationBean<UserDataPermissionRpcWebFilter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(new UserDataPermissionRpcWebFilter());
        registrationBean.setOrder(USER_DATA_PERMISSION_FILTER);
        return registrationBean;
    }

}
