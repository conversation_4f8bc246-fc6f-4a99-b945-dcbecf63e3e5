package com.linkcircle.boss.module.charge.crm.constants;

import com.linkcircle.boss.framework.common.exception.ErrorCode;

/**
 * <AUTHOR>
 * @date 2025-06-05 9:54
 * @description 错误码 <p>
 * charge 系统，使用 1-005-000-000 段
 * 1-005_业务模块_序号
 */
public interface ErrorCodeConstants {

    // ========== 量表管理模块 1-005-001-000 ==========
    ErrorCode SCALE_INFO_NOT_EXISTS = new ErrorCode(1_005_001_001, "量表信息不存在");
    ErrorCode SCALE_INFO_TABLE_CODE_DUPLICATE = new ErrorCode(1_005_001_002, "量表表名已存在");
    ErrorCode SCALE_COLUMN_INFO_NOT_EXISTS = new ErrorCode(1_005_001_003, "量表字段配置不存在");
    ErrorCode SCALE_SYSTEM_COLUMN_NOT_EXISTS = new ErrorCode(1_005_001_004, "量表系统字段配置不存在");
    ErrorCode SCALE_TYPE_NOT_EXISTS = new ErrorCode(1_005_001_005, "量表类型不存在");
    ErrorCode DATASOURCE_NOT_EXISTS = new ErrorCode(1_005_010_006, "数据源不存在: {}");

    // ========== 供应商模块 1-005-002-000 ==========
    ErrorCode SUPPLIER_NAME_DUPLICATE = new ErrorCode(1_005_002_000, "供应商名称已存在");
    ErrorCode SUPPLIER_NOT_EXISTS = new ErrorCode(1_005_002_001, "供应商不存在");
    ErrorCode SUPPLIER_NOT_ARCHIVE = new ErrorCode(1_005_002_002, "供应商状态不为已存档");
    ErrorCode SUPPLIER_HAS_PURCHASE_RECORD = new ErrorCode(1_005_002_003, "供应商存在采购记录");
    ErrorCode SUPPLIER_NO_COOPERATION = new ErrorCode(1_005_002_004, "供应商状态不为未合作,不能存档");
    ErrorCode SUPPLIER_HAS_UNFINISHED_PURCHASE_RECORD = new ErrorCode(1_005_002_005, "供应商存在未完成的采购记录");
    ErrorCode SUPPLIER_ACCOUNT_ID_DUPLICATE = new ErrorCode(1_005_002_006, "供应商账户已存在");
    ErrorCode SUPPLIER_ACCOUNT_NOT_EXISTS = new ErrorCode(1_005_002_007, "供应商账户不存在");
    ErrorCode SUPPLIER_ACCOUNT_EXPORT_ERROR = new ErrorCode(1_005_002_008, "供应商账户导出异常");
    ErrorCode PURCHASE_VALID_RECORD_EXIST = new ErrorCode(1_005_002_009, "供应商存在有效的采购记录");
    ErrorCode PURCHASE_RECORD_EXIST = new ErrorCode(1_005_002_010, "供应商存在采购记录");
    // 项目模块 1-005-003-000
    ErrorCode PROJECT_REPORT_EXCEPTION = new ErrorCode(1_005_003_000, "项目信息导出异常");
    ErrorCode PROJECT_FILE_TYPE_ERROR = new ErrorCode(1_005_003_001, "不支持的文件类型");
    ErrorCode CREATE_FAILED = new ErrorCode(1_005_003_002, "创建失败");
    ErrorCode UPDATE_FAILED = new ErrorCode(1_005_003_003, "更新失败");
    ErrorCode DELETE_FAILED = new ErrorCode(1_005_003_004, "删除失败");
    ErrorCode PROJECT_CONTRACT_NOT_END = new ErrorCode(1_005_003_005, "合同未结束");
    ErrorCode PROJECT_NOT_EXIST = new ErrorCode(1_005_003_006, "项目不存在");
    ErrorCode PROJECT_ACCOUNT_NOT_EXIST = new ErrorCode(1_005_003_007, "负责人账号不存在");
    ErrorCode PROJECT_ACCOUNT_NOT_ACTIVATE = new ErrorCode(1_005_003_008, "负责人账号未激活");
    ErrorCode PROJECT_TIME_ERROR = new ErrorCode(1_005_003_009, "结束时间不能小于开始时间");
    ErrorCode PROJECT_NAME_OR_ID_ERROR = new ErrorCode(1_005_003_010, "项目名称或ID重复");
    ErrorCode PROJECT_ONLY_ALLOW_PERFORM = new ErrorCode(1_005_003_011, "仅允许暂停进行中的项目");
    ErrorCode PROJECT_NOT_END = new ErrorCode(1_005_003_012, "项目未结项");

    // 合同模块 1-005-004-000
    ErrorCode CONTRACT_ID_OR_CODE_ERROR = new ErrorCode(1_005_004_000, "合同ID或编号重复");
    ErrorCode CONTRACT_ONLY_ALLOW_NOT_START = new ErrorCode(1_005_004_001, "仅允许删除待开始合同");
    ErrorCode CONTRACT_EXIST_SUBSCRIBE = new ErrorCode(1_005_004_002, "合同存在关联订阅");
    ErrorCode CONTRACT_NOT_EXIST = new ErrorCode(1_005_004_003, "合同信息不存在");
    ErrorCode CONTRACT_REPORT_EXCEPTION = new ErrorCode(1_005_004_004, "合同信息导出异常");

    // 优惠模块 1-005-005-000
    ErrorCode COUPON_EXPORT_EXCEPTION = new ErrorCode(1_005_006_000, "优惠信息导出异常");

    // 计划模块 1-005-006-000
    ErrorCode PLAN_NOT_EXISTS = new ErrorCode(1_005_006_000, "计划信息不存在");
    ErrorCode NAME_TOO_LONG = new ErrorCode(1_005_006_001, "复制后的计划名称过长");
    ErrorCode PLAN_PRODUCT_CANNOT_BE_EMPTY = new ErrorCode(1_005_006_002, "计划产品不能为空");
    ErrorCode DISCOUNT_AMOUNT_EMPTY = new ErrorCode(1_005_006_003, "折扣金额不能为空");

    // 订阅模块 1-005-007-000
    ErrorCode SUBSCRIBE_NOT_EXISTS = new ErrorCode(1_005_007_000, "订阅信息不存在");
    ErrorCode SUBSCRIBE_EXPORT_EXCEPTION = new ErrorCode(1_005_007_001, "订阅信息导出异常");

    // 资源模块 1-005-008-000
    ErrorCode RESOURCE_NAME_REPEATED_EXCEPTION = new ErrorCode(1_005_008_001, "资源名称重复");
    ErrorCode RESOURCE_SERVICE_VERSION_ADD_EXCEPTION = new ErrorCode(1_005_008_002, "新版本必须大于之前最大版本");
    ErrorCode RESOURCE_SERVICE_VERSION_MAIN_EXCEPTION = new ErrorCode(1_005_008_003, "次版本9后，主版本需要加1");
    ErrorCode RESOURCE_SERVICE_VERSION_DTO_EXCEPTION = new ErrorCode(1_005_008_004, "不正确的请求类型");
    ErrorCode RESOURCE_SERVICE_NAME_REPEATED_EXCEPTION = new ErrorCode(1_005_008_005, "服务名称重复");
    ErrorCode RESOURCE_SERVICE_SCALE_UNCONFIGURED_EXCEPTION = new ErrorCode(1_005_008_006, "请先配置量表");
    ErrorCode RESOURCE_SERVICE_UN_NEED_CONFIGURED_EXCEPTION = new ErrorCode(1_005_008_007, "梯度费率与固定费率不需要配置向量");
    ErrorCode RESOURCE_SERVICE_BE_PURCHASE_EXCEPTION = new ErrorCode(1_005_008_008, "当前资源服务已被采购");
    ErrorCode RESOURCE_SERVICE_PURCHASE_RESOURCE_SERVICE_REPEATED_EXCEPTION = new ErrorCode(1_005_008_009, "采购资源服务重复");

    // 主体模块 1-***********
    ErrorCode ENTITY_EXIST_ASSOCIATION_ACCOUNT = new ErrorCode(1_005_009_000, "存在关联账户");
    ErrorCode ENTITY_EXIST_ASSOCIATION_CUSTOMER = new ErrorCode(1_005_009_001, "存在关联客户");
    ErrorCode ENTITY_NOT_EXIST = new ErrorCode(1_005_009_002, "主体信息不存在");
    ErrorCode ENTITY_ID_ERROR = new ErrorCode(1_005_009_003, "主体ID已经存在");
    ErrorCode ENTITY_EMAIL_ERROR = new ErrorCode(1_005_009_004, "邮箱格式错误");
    ErrorCode ENTITY_TAX_NUMBER_ERROR = new ErrorCode(1_005_009_005, "税号格式错误");

    // ========== 指标单位配置模块 1-*********** ==========
    ErrorCode METRIC_UNIT_CONFIG_NOT_EXISTS = new ErrorCode(1_005_010_000, "指标单位配置不存在");
    ErrorCode METRIC_UNIT_CONFIG_NAME_DUPLICATE = new ErrorCode(1_005_010_001, "指标单位配置名称已存在");
    ErrorCode METRIC_UNIT_CONFIG_EXISTS_CHILDREN = new ErrorCode(1_005_010_002, "存在子节点,无法删除");
}
