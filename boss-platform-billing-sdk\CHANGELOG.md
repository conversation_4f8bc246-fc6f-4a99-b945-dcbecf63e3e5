# 更新日志

## [1.0.0] - 2025-07-25

### 新增功能
- ✨ 初始版本发布
- ✨ 支持收入账单明细推送
- ✨ 支持成本账单明细推送
- ✨ 自动签名验证和参数校验
- ✨ 内置重试机制
- ✨ 支持调试模式
- ✨ 链式API和构建器模式
- ✨ 完善的异常处理体系
- ✨ 兼容JDK 8和JDK 21

### 技术特性
- 🔐 基于SHA256的签名算法
- 🔄 自动重试机制（可配置）
- 📝 详细的调试日志
- ⚡ 基于OkHttp的高性能HTTP客户端
- 🛡️ 完善的参数验证
- 📦 零依赖Spring，可在任何Java项目中使用

### API接口
- `/billing/api/v3/income/charge-income-bill-detail` - 收入账单明细推送
- `/billing/api/v3/cost/charge-cost-bill-detail` - 成本账单明细推送

### 依赖版本
- Hutool: 5.8.25
- Jackson: 2.15.3
- OkHttp: 4.12.0
- SLF4J: 1.7.36
