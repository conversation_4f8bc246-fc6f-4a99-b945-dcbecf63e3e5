package com.linkcircle.boss.module.charge.fee.web.payment.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class WalletBalanceDoPrepaidUnPayDTO {

    /**
     * 支付钱包id
     */
    @Schema(description = "钱包ID")
    private Long walletId;

    /**
     * 再次发起支付的 预付费账单集合
     */
    @Schema(description = "再次发起支付的预付费账单集合")
    @NotNull(message = "再次发起支付的预付费账单集合不能为空")
    @NotEmpty(message = "再次发起支付的预付费账单集合不能为空")
    private List<Long> prepaidIncomeBillIds;

    /**
     * 批量账单交易的最早最晚时间 (分表规则)
     */
    @Schema(description = "批量账单交易的最早最晚时间")
    @NotNull(message = "批量账单交易的最早最晚时间不能为空")
    @NotEmpty(message = "批量账单交易的最早最晚时间不能为空")
    private List<Long> startToEndTimeRange;

    /**
     * 服务码集合 (分表规则)
     */
    @Schema(description = "服务码集合")
    @NotNull(message = "服务码集合不能为空")
    @NotEmpty(message = "服务码集合不能为空")
    private List<String> serviceCodes;

}
