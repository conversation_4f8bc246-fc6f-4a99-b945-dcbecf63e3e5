package com.linkcircle.boss.module.charge.fee.web.bill.service;

import com.linkcircle.boss.module.charge.fee.web.bill.model.dto.calculate.BillFeeCalculateReqDTO;
import com.linkcircle.boss.module.charge.fee.web.bill.model.dto.calculate.BillProductFeeCalculateReqDTO;
import com.linkcircle.boss.module.charge.fee.web.bill.model.vo.calculate.BillFeeResponseVO;
import com.linkcircle.boss.module.charge.fee.web.bill.model.vo.calculate.BillProductFeeResponseVO;
import com.linkcircle.boss.module.charge.fee.web.bill.service.impl.fee.common.JsonApi;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/7/15 14:11
 */
public interface BillFeeService extends JsonApi {
    BillProductFeeResponseVO computeProductServiceFee(BillProductFeeCalculateReqDTO productFeeReq);

    BillFeeResponseVO computeBillFee(BillFeeCalculateReqDTO calculateReq);

    public BillFeeResponseVO  createBill(BillFeeCalculateReqDTO calculateReq);


    public BillFeeResponseVO  makeSureBill(BillFeeCalculateReqDTO calculateReq);

    Long computeCustomerUnPaidBillNum(Long customerId);

    BigDecimal computeConstructFee(Long constructId, Long startTime, Long endTime);
}
