package com.linkcircle.boss.billing.sdk.spring;

import com.linkcircle.boss.billing.sdk.BillingClient;
import com.linkcircle.boss.billing.sdk.exception.BillingException;
import com.linkcircle.boss.billing.sdk.model.request.CostBillDetailRequest;
import com.linkcircle.boss.billing.sdk.model.request.IncomeBillDetailRequest;
import com.linkcircle.boss.billing.sdk.model.response.CostBillDetailResponse;
import com.linkcircle.boss.billing.sdk.model.response.IncomeBillDetailResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025-07-25 15:00
 * @description 计费服务Spring封装
 */
@Service
@ConditionalOnBean(BillingClient.class)
public class BillingService {

    private static final Logger logger = LoggerFactory.getLogger(BillingService.class);

    @Autowired
    private BillingClient billingClient;

    /**
     * 推送收入账单明细
     *
     * @param request 收入账单明细请求
     * @return 收入账单明细响应
     * @throws BillingException 计费异常
     */
    public IncomeBillDetailResponse pushIncomeBillDetail(IncomeBillDetailRequest request) throws BillingException {
        logger.info("推送收入账单明细, businessId: {}, accountId: {}", 
                request.getBusinessId(), request.getAccountId());
        
        try {
            IncomeBillDetailResponse response = billingClient.pushIncomeBillDetail(request);
            logger.info("推送收入账单明细成功, billDetailId: {}", response.getBillDetailId());
            return response;
        } catch (BillingException e) {
            logger.error("推送收入账单明细失败, businessId: {}, error: {}", 
                    request.getBusinessId(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 推送成本账单明细
     *
     * @param request 成本账单明细请求
     * @return 成本账单明细响应
     * @throws BillingException 计费异常
     */
    public CostBillDetailResponse pushCostBillDetail(CostBillDetailRequest request) throws BillingException {
        logger.info("推送成本账单明细, businessId: {}, accountId: {}", 
                request.getBusinessId(), request.getAccountId());
        
        try {
            CostBillDetailResponse response = billingClient.pushCostBillDetail(request);
            logger.info("推送成本账单明细成功, billDetailId: {}", response.getBillDetailId());
            return response;
        } catch (BillingException e) {
            logger.error("推送成本账单明细失败, businessId: {}, error: {}", 
                    request.getBusinessId(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 异步推送收入账单明细
     *
     * @param request 收入账单明细请求
     */
    public void pushIncomeBillDetailAsync(IncomeBillDetailRequest request) {
        // 可以使用@Async注解或者线程池来实现异步推送
        new Thread(() -> {
            try {
                pushIncomeBillDetail(request);
            } catch (Exception e) {
                logger.error("异步推送收入账单明细失败, businessId: {}", request.getBusinessId(), e);
            }
        }).start();
    }

    /**
     * 异步推送成本账单明细
     *
     * @param request 成本账单明细请求
     */
    public void pushCostBillDetailAsync(CostBillDetailRequest request) {
        // 可以使用@Async注解或者线程池来实现异步推送
        new Thread(() -> {
            try {
                pushCostBillDetail(request);
            } catch (Exception e) {
                logger.error("异步推送成本账单明细失败, businessId: {}", request.getBusinessId(), e);
            }
        }).start();
    }
}
