package com.linkcircle.boss.billing.sdk.model.request;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025-07-25 15:00
 * @description 账单明细请求基类
 */
public abstract class BaseBillDetailRequest {

    /**
     * 账户ID
     */
    @JsonProperty("account_id")
    private Long accountId;

    /**
     * 业务产生的时间戳(毫秒)
     */
    @JsonProperty("business_time")
    private Long businessTime;

    /**
     * 消耗量
     */
    @JsonProperty("usage")
    private BigDecimal usage;

    /**
     * 消耗量单位
     */
    @JsonProperty("usage_unit")
    private String usageUnit;

    /**
     * 扣费回调URL
     */
    @JsonProperty("callback_url")
    private String callbackUrl;

    /**
     * 业务话单数据
     */
    @JsonProperty("data")
    private Map<String, Object> data;

    /**
     * 接口请求时间戳(毫秒) - SDK自动填充
     */
    @JsonProperty("timestamp")
    private Long timestamp;

    /**
     * 参数签名 - SDK自动填充
     */
    @JsonProperty("sign")
    private String sign;

    /**
     * 请求ID - SDK自动生成
     */
    @JsonProperty("request_id")
    private String requestId;

    /**
     * 业务唯一ID
     */
    @JsonProperty("business_id")
    private String businessId;

    public BaseBillDetailRequest() {
    }

    public Long getAccountId() {
        return accountId;
    }

    public void setAccountId(Long accountId) {
        this.accountId = accountId;
    }

    public Long getBusinessTime() {
        return businessTime;
    }

    public void setBusinessTime(Long businessTime) {
        this.businessTime = businessTime;
    }

    public BigDecimal getUsage() {
        return usage;
    }

    public void setUsage(BigDecimal usage) {
        this.usage = usage;
    }

    public String getUsageUnit() {
        return usageUnit;
    }

    public void setUsageUnit(String usageUnit) {
        this.usageUnit = usageUnit;
    }

    public String getCallbackUrl() {
        return callbackUrl;
    }

    public void setCallbackUrl(String callbackUrl) {
        this.callbackUrl = callbackUrl;
    }

    public Map<String, Object> getData() {
        return data;
    }

    public void setData(Map<String, Object> data) {
        this.data = data;
    }

    public Long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getBusinessId() {
        return businessId;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }
}
