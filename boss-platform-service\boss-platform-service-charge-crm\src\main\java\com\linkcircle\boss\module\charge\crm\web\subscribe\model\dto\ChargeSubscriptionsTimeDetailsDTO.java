package com.linkcircle.boss.module.charge.crm.web.subscribe.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 订阅持续时间表实体类
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChargeSubscriptionsTimeDetailsDTO {

    @Schema(description = "订阅阶段持续时间id")
    private Long id;

    @Schema(description = "订阅id")
    private Long subsId;

    @NotNull(message = "订阅开始时间不能为空")
    @Schema(description = "订阅开始时间")
    private Long startTime;

    @NotNull(message = "订阅结束时间不能为空")
    @Schema(description = "订阅结束时间")
    private Long endTime;

    @Schema(description = "免费试用天数")
    private Integer freeTryoutDays;

    @Schema(description = "是否按比例计算，0：否，1：是")
    private Integer byProportion;

    @Schema(description = "产品列表")
    private List<ChargeSubscriptionsProductDTO> products;
}