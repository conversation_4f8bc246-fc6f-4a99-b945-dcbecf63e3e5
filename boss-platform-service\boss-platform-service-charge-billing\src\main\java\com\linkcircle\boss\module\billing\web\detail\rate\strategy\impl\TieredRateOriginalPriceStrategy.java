package com.linkcircle.boss.module.billing.web.detail.rate.strategy.impl;

import cn.hutool.core.lang.Pair;
import com.linkcircle.boss.module.billing.api.rate.model.dto.TierRateConfigDTO;
import com.linkcircle.boss.module.billing.constants.RateTypeConstant;
import com.linkcircle.boss.module.billing.enums.OriginalPriceRateTypeEnum;
import com.linkcircle.boss.module.billing.web.detail.rate.strategy.context.OriginalPriceCalculateRequest;
import com.linkcircle.boss.module.billing.web.detail.rate.strategy.context.OriginalPriceCalculateResponse;
import com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.Coupon;
import io.github.kk01001.design.pattern.strategy.IStrategy;
import io.github.kk01001.design.pattern.strategy.annotation.Strategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-06-24 13:43
 * @description 阶梯费率原价计算策略
 */
@Slf4j
@Component
@RequiredArgsConstructor
@Strategy(strategyEnum = OriginalPriceRateTypeEnum.class, strategyType = RateTypeConstant.TIERED)
public class TieredRateOriginalPriceStrategy extends AbstractOriginalPriceStrategy implements IStrategy<OriginalPriceCalculateRequest, OriginalPriceCalculateResponse> {

    @Override
    public OriginalPriceCalculateResponse execute(OriginalPriceCalculateRequest request) {
        TierRateConfigDTO rateConfig = (TierRateConfigDTO) request.getRateConfig();
        OriginalPriceCalculateResponse response = calculateTieredOriginalPrice(request, rateConfig);
        inTrial(request, response);
        response.setChargeUsageCount(request.getCurrentUsage());

        calculateTax(request, response);
        response.setRateConfig(rateConfig);
        response.setCouponList(request.getCouponList());

        response.setDiscountAmount(response.getOriginalPrice().subtract(response.getDiscountedPrice()));
        return response;
    }

    /**
     * 计算阶梯费率目录价（基于累计用量）
     *
     * @param rateConfig 阶梯费率配置
     * @param request    计费请求
     * @return 本次计费金额
     */
    public OriginalPriceCalculateResponse calculateTieredOriginalPrice(OriginalPriceCalculateRequest request,
                                                                       TierRateConfigDTO rateConfig) {
        OriginalPriceCalculateResponse response = OriginalPriceCalculateResponse.success();
        response.setOriginalPrice(BigDecimal.ZERO);
        response.setDiscountedPrice(BigDecimal.ZERO);
        response.setMeasure(BigDecimal.ONE);
        response.setMeasureCeil(0);

        BigDecimal totalUsageWithCurrent = request.getTotalUsageWithCurrent();
        BigDecimal previousUsage = request.getPreviousUsage();
        BigDecimal currentUsage = request.getCurrentUsage();
        response.setChargeUnitCount(currentUsage);

        List<TierRateConfigDTO.TierPriceDTO> tierPrices = rateConfig.getTierPrices();
        if (tierPrices == null || tierPrices.isEmpty()) {
            return response;
        }

        log.debug("开始计算阶梯费率 - 之前累计用量: {}, 本次用量: {}, 总累计用量: {}",
                previousUsage, currentUsage, totalUsageWithCurrent);

        BigDecimal originalPriceTotal = BigDecimal.ZERO;
        BigDecimal discountPriceTotal = BigDecimal.ZERO;

        // 已处理的用量
        BigDecimal processedUsage = previousUsage;
        // 剩余需要计费的用量
        BigDecimal remainingUsage = currentUsage;

        // 遍历阶梯配置，计算本次用量在各个阶梯中的费用
        for (TierRateConfigDTO.TierPriceDTO tierPrice : tierPrices) {
            tierPrice.setUsage(BigDecimal.ZERO);
            // 本次用量已全部计算完毕
            if (remainingUsage.compareTo(BigDecimal.ZERO) <= 0) {
                break;
            }

            // 1. 先计算当前累计用量在哪个阶梯
            BigDecimal tierMin = tierPrice.getMin();
            BigDecimal tierMax = tierPrice.getMax();

            // 处理无限大的情况（max为-1）
            if (tierMax.compareTo(BigDecimal.valueOf(-1)) == 0) {
                tierMax = BigDecimal.valueOf(Long.MAX_VALUE);
            }

            log.debug("处理阶梯 [{} - {}], 已处理用量: {}, 剩余用量: {}",
                    tierMin, tierMax, processedUsage, remainingUsage);

            // 判断当前阶梯是否与本次用量有交集
            if (processedUsage.compareTo(tierMax) >= 0) {
                // 已处理用量已超过当前阶梯上限，跳过
                continue;
            }

            // 计算本次用量在当前阶梯中的部分
            // 在当前阶梯中的起始位置
            BigDecimal tierUsageStart = processedUsage.max(tierMin);
            // 在当前阶梯中的结束位置
            BigDecimal tierUsageEnd = totalUsageWithCurrent.min(tierMax);
            BigDecimal usageInThisTier = tierUsageEnd.subtract(tierUsageStart).max(BigDecimal.ZERO);

            if (usageInThisTier.compareTo(BigDecimal.ZERO) <= 0) {
                // 本次用量在当前阶梯中没有消费
                continue;
            }

            log.debug("本次用量在阶梯 [{} - {}] 中消费: {}", tierMin, tierMax, usageInThisTier);

            // 3. 根据支付类型和全额支付配置计算金额
            Pair<BigDecimal, BigDecimal> tierAmountPair = calculateTierAmount(request, tierPrice, usageInThisTier, tierMin, tierMax);

            originalPriceTotal = originalPriceTotal.add(tierAmountPair.getKey());
            discountPriceTotal = discountPriceTotal.add(tierAmountPair.getValue());

            // 更新已处理的用量
            processedUsage = tierUsageEnd;
            // 更新剩余用量
            remainingUsage = remainingUsage.subtract(usageInThisTier);
        }

        log.info("阶梯费率计算完成 - 本次计费金额, 目录价: {}, 优惠价: {}", originalPriceTotal, discountPriceTotal);
        response.setOriginalPrice(originalPriceTotal);
        response.setDiscountedPrice(discountPriceTotal);
        return response;
    }

    /**
     * 计算单个阶梯的费用
     *
     * @param tierPrice       阶梯价格配置
     * @param usageInThisTier 在当前阶梯的用量
     * @param tierMin         当前阶梯下限
     * @param tierMax         当前阶梯上限
     * @return 当前阶梯的费用
     */
    private Pair<BigDecimal, BigDecimal> calculateTierAmount(OriginalPriceCalculateRequest request,
                                                             TierRateConfigDTO.TierPriceDTO tierPrice,
                                                             BigDecimal usageInThisTier,
                                                             BigDecimal tierMin,
                                                             BigDecimal tierMax) {
        BigDecimal previousUsage = request.getPreviousUsage();
        Integer paymentOptions = request.getPaymentOptions();
        List<Coupon> couponList = request.getCouponList();

        tierPrice.setUsage(usageInThisTier);

        // 4. 如果有配置全额支付
        if (tierPrice.getIsAllPay() != null && tierPrice.getIsAllPay() == 1) {
            return calculateAllPayTierAmount(tierPrice, couponList, usageInThisTier, paymentOptions, previousUsage, tierMin);
        }

        // 2. 根据单位计算当前这次的用量占几个单位
        BigDecimal payUnit = BigDecimal.valueOf(tierPrice.getPayUnit());
        // 消耗量/每单位 * 价格
        BigDecimal units = usageInThisTier.divide(payUnit, 0, RoundingMode.HALF_UP);

        // 目录价
        BigDecimal originalUnitPrice = getUnitPrice(paymentOptions, tierPrice.getFixCharge(), tierPrice.getIntegralCharge());
        // 按用量计算：支付类型的单价 * 单位数
        BigDecimal originalPrice = getTotalPrice(paymentOptions, units, originalUnitPrice);
        tierPrice.setOriginalUnitPrice(originalUnitPrice);
        tierPrice.setOriginalPrice(originalPrice);

        // 订阅价
        BigDecimal discountUnitPrice = calculateDiscountPrice(originalUnitPrice, couponList);
        BigDecimal discountPrice = getTotalPrice(paymentOptions, units, discountUnitPrice);
        tierPrice.setDiscountedUnitPrice(discountUnitPrice);
        tierPrice.setDiscountedPrice(discountPrice);

        tierPrice.setChargeUnitCount(usageInThisTier);
        tierPrice.setIsHit(true);

        log.debug("阶梯 [{} - {}] 计费: 用量={}, 单位={}, 单位数={}, 目录价: {}, 优惠价: {}",
                tierMin, tierMax, usageInThisTier, payUnit, units, originalPrice, discountPrice);
        return Pair.of(originalPrice, discountPrice);
    }

    /**
     * 计算全额支付阶梯的费用（新逻辑）
     *
     * @param tierPrice       阶梯价格配置
     * @param usageInThisTier 在当前阶梯的用量
     * @param paymentOptions  支付方式 0-现金 1-积分
     * @param previousUsage   之前累计用量
     * @param tierMin         当前阶梯下限
     * @return 该阶梯的费用 目录价-优惠价
     */
    private Pair<BigDecimal, BigDecimal> calculateAllPayTierAmount(TierRateConfigDTO.TierPriceDTO tierPrice,
                                                                   List<Coupon> couponList,
                                                                   BigDecimal usageInThisTier,
                                                                   Integer paymentOptions,
                                                                   BigDecimal previousUsage,
                                                                   BigDecimal tierMin) {

        // 判断之前是否已经进入过该阶梯
        boolean hasEnteredThisTierBefore = previousUsage.compareTo(tierMin) > 0;

        if (hasEnteredThisTierBefore) {
            // 之前已经进入过该阶梯，说明已经付过全额费用，本次免费
            log.debug("阶梯{}之前已付费，本次使用免费", tierPrice.getTierLevel());
            return Pair.of(BigDecimal.ZERO, BigDecimal.ZERO);
        }

        // 第一次进入该阶梯，需要按全额支付计算
        // 计算阶梯的范围大小（左开右闭区间：max - min）
        BigDecimal tierRangeSize;
        if (tierPrice.getMax().compareTo(BigDecimal.valueOf(-1)) == 0) {
            // 处理无限大(值为-1)的情况，如果是无限大阶梯，则按实际用量计算
            tierRangeSize = usageInThisTier;
        } else {
            // 阶梯范围大小 = max - min（左开右闭区间）
            tierRangeSize = tierPrice.getMax().subtract(tierPrice.getMin());
        }

        // 计算阶梯范围大小对应的单位数 TODO 四舍五入? 保留整数?
        BigDecimal maxUnits = tierRangeSize.divide(BigDecimal.valueOf(tierPrice.getPayUnit()), 0, RoundingMode.HALF_UP);

        // 根据支付方式计算目录价
        BigDecimal unitPrice = getUnitPrice(paymentOptions, tierPrice.getFixCharge(), tierPrice.getIntegralCharge());
        BigDecimal originalPrice = getTotalPrice(paymentOptions, maxUnits, unitPrice);
        tierPrice.setOriginalUnitPrice(unitPrice);
        tierPrice.setOriginalPrice(originalPrice);

        // 优惠价
        BigDecimal discountUnitPrice = calculateDiscountPrice(unitPrice, couponList);
        BigDecimal discountPrice = getTotalPrice(paymentOptions, maxUnits, discountUnitPrice);
        tierPrice.setDiscountedUnitPrice(discountUnitPrice);
        tierPrice.setDiscountedPrice(discountPrice);

        log.debug("阶梯{}首次进入，阶梯范围[{}-{}]，范围大小: {}，全额支付, 目录价: {}, 优惠价: {}",
                tierPrice.getTierLevel(), tierPrice.getMin(), tierPrice.getMax(), tierRangeSize, originalPrice, discountPrice);
        tierPrice.setChargeUnitCount(tierRangeSize);
        tierPrice.setIsHit(true);
        return Pair.of(originalPrice, discountPrice);
    }
}
