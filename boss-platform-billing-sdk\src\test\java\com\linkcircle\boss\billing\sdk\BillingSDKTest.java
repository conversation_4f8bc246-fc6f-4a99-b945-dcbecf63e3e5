package com.linkcircle.boss.billing.sdk;

import com.linkcircle.boss.billing.sdk.config.BillingConfig;
import com.linkcircle.boss.billing.sdk.model.request.CostBillDetailRequest;
import com.linkcircle.boss.billing.sdk.model.request.IncomeBillDetailRequest;
import com.linkcircle.boss.billing.sdk.model.response.CostBillDetailResponse;
import com.linkcircle.boss.billing.sdk.model.response.IncomeBillDetailResponse;
import org.junit.Before;
import org.junit.Test;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025-07-25 15:00
 * @description 计费SDK测试类
 */
public class BillingSDKTest {

    private BillingClient billingClient;

    @Before
    public void setUp() {
        // 配置测试环境
        BillingConfig config = BillingConfig.builder()
                .baseUrl("http://localhost:49086")  // 计费服务地址
                .appId("test_app_id")
                .appSecret("test_app_secret")
                .tenantId(1L)
                .debug(true)  // 开启调试模式
                .build();

        billingClient = new BillingClient(config);
    }

    @Test
    public void testPushIncomeBillDetail() {
        try {
            // 构建收入账单明细请求
            IncomeBillDetailRequest request = BillingSDK.incomeRequest()
                    .accountId(123456L)
                    .productId(789L)
                    .serviceId(456L)
                    .businessTime(System.currentTimeMillis())
                    .usage(100.5)
                    .usageUnit("minutes")
                    .callbackUrl("https://example.com/callback")
                    .businessId("biz_" + System.currentTimeMillis())
                    .addData("call_duration", 100.5)
                    .addData("call_type", "voice")
                    .addData("from_number", "***********")
                    .addData("to_number", "***********")
                    .build();

            // 推送收入账单明细
            IncomeBillDetailResponse response = billingClient.pushIncomeBillDetail(request);

            System.out.println("推送收入账单明细成功:");
            System.out.println("账单明细ID: " + response.getBillDetailId());
            System.out.println("账户ID: " + response.getAccountId());
            System.out.println("产品ID: " + response.getProductId());
            System.out.println("服务ID: " + response.getServiceId());

        } catch (Exception e) {
            System.err.println("推送收入账单明细失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testPushCostBillDetail() {
        try {
            // 构建成本账单明细请求
            CostBillDetailRequest request = BillingSDK.costRequest()
                    .accountId(123456L)
                    .resourceId(789L)
                    .resourceServiceId(456L)
                    .businessTime(System.currentTimeMillis())
                    .usage(50.0)
                    .usageUnit("GB")
                    .callbackUrl("https://example.com/callback")
                    .businessId("cost_biz_" + System.currentTimeMillis())
                    .addData("bandwidth", 50.0)
                    .addData("region", "cn-beijing")
                    .addData("instance_type", "ecs.t5.large")
                    .build();

            // 推送成本账单明细
            CostBillDetailResponse response = billingClient.pushCostBillDetail(request);

            System.out.println("推送成本账单明细成功:");
            System.out.println("账单明细ID: " + response.getBillDetailId());
            System.out.println("账户ID: " + response.getAccountId());
            System.out.println("资源ID: " + response.getResourceId());
            System.out.println("资源服务ID: " + response.getResourceServiceId());

        } catch (Exception e) {
            System.err.println("推送成本账单明细失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testBatchPush() {
        try {
            // 批量推送示例
            for (int i = 0; i < 5; i++) {
                IncomeBillDetailRequest request = BillingSDK.incomeRequest()
                        .accountId(123456L)
                        .productId(789L)
                        .serviceId(456L)
                        .businessTime(System.currentTimeMillis())
                        .usage(BigDecimal.valueOf(10 + i))
                        .usageUnit("seconds")
                        .callbackUrl("https://example.com/callback")
                        .businessId("batch_biz_" + System.currentTimeMillis() + "_" + i)
                        .addData("batch_index", i)
                        .addData("batch_total", 5)
                        .build();

                IncomeBillDetailResponse response = billingClient.pushIncomeBillDetail(request);
                System.out.println("批量推送第" + (i + 1) + "条成功, 账单明细ID: " + response.getBillDetailId());

                // 避免请求过快
                Thread.sleep(100);
            }

        } catch (Exception e) {
            System.err.println("批量推送失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testConfigBuilder() {
        // 测试配置构建器
        BillingConfig config = BillingConfig.builder()
                .baseUrl("http://localhost:49086")
                .appId("test_app")
                .appSecret("test_secret")
                .tenantId(1L)
                .connectTimeout(30000)
                .readTimeout(60000)
                .maxRetries(3)
                .debug(true)
                .build();

        System.out.println("配置创建成功:");
        System.out.println("BaseURL: " + config.getBaseUrl());
        System.out.println("AppId: " + config.getAppId());
        System.out.println("TenantId: " + config.getTenantId());
        System.out.println("Debug: " + config.isDebug());
    }
}
