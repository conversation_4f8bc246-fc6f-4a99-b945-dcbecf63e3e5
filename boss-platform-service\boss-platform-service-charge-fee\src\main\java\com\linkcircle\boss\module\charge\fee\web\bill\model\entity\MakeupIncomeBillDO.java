package com.linkcircle.boss.module.charge.fee.web.bill.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-07-02 15:20:28
 * @description 手工账单-收入-账单表实体类
 */
@TableName("makeup_income_bill")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "手工账单-收入-账单表")
public class MakeupIncomeBillDO {

    /**
     * 账单id
     */
    @TableId(type = IdType.AUTO)
    @Schema(description = "账单id")
    private Long billId;


    @TableField("bill_no")
    @Schema(description = "账单号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String billNo;

    /**
     * 开票退款金额
     */
    @TableField("refund_invoice_amount")
    @Schema(description = "开票退款金额")
    private BigDecimal refundInvoiceAmount;
    /**
     * 客户id
     */
    @TableField("customer_id")
    @Schema(description = "客户id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long customerId;

    /**
     * 账户id
     */
    @TableField("account_id")
    @Schema(description = "账户id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long accountId;

    /**
     * 主体id
     */
    @TableField("entity_id")
    @Schema(description = "主体id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long entityId;

    /**
     * 合同id
     */
    @TableField("contract_id")
    @Schema(description = "合同id")
    private Long contractId;

    /**
     * 钱包id
     */
    @TableField("wallet_id")
    @Schema(description = "钱包id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long walletId;

    /**
     * 账单状态	 0-草稿, 1-待支付, 2-已支付, 3-未结清
     */
    @TableField("bill_status")
    @Schema(description = "账单状态 0-草稿, 1-待支付, 2-已支付, 3-未结清", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer billStatus;

    /**
     * 税率
     */
    @TableField("tax_rate")
    @Schema(description = "税率")
    private BigDecimal taxRate;

    /**
     * 税率
     */
    @TableField("tax_amount")
    @Schema(description = "税率金额总计")
    private BigDecimal taxAmount;

    // 各个服务总价  = 各个服务原价  - 折扣  + 税率
    @TableField("sub_total_amount")
    @Schema(description = "总价=各个服务总价之和")
    private BigDecimal subTotalAmount;

    /**
     * 优惠的金额
     */
    @TableField("discount_amount")
    @Schema(description = "账单优惠的金额")
    private BigDecimal discountAmount;


    /**
     * 已开票金额
     */
    @TableField("invoiced_amount")
    @Schema(description = "已开票金额")
    private BigDecimal invoicedAmount;

    /**
     * 可开票金额(=优惠价-已开票金额)
     */
    @TableField("available_invoice_amount")
    @Schema(description = "可开票金额(=优惠价-已开票金额)")
    private BigDecimal availableInvoiceAmount;

    /**
     * 含税总金额->发票金额[可开发票金额的最大值]
     */
    @TableField("amount_with_tax")
    @Schema(description = "含税总金额->发票金额[可开发票金额的最大值]")
    private BigDecimal amountWithTax;

    /**
     * 不含税金额
     */
    @TableField("amount_without_tax")
    @Schema(description = "不含税金额")
    private BigDecimal amountWithoutTax;

    /**
     * 货币单位 CNY USD
     */
    @TableField("currency")
    @Schema(description = "货币单位 CNY USD")
    private String currency;

    /**
     * 实际支付时间戳
     */
    @TableField("payment_time")
    @Schema(description = "实际支付时间戳")
    private Long paymentTime;

    /**
     * 出账时间戳
     */
    @TableField("billing_time")
    @Schema(description = "出账时间戳")
    private Long billingTime;

    /**
     * 账期-开始时间时间戳
     */
    @TableField("billing_start_time")
    @Schema(description = "账期-开始时间时间戳")
    private Long billingStartTime;

    /**
     * 账期-结束时间时间戳
     */
    @TableField("billing_end_time")
    @Schema(description = "账期-结束时间时间戳")
    private Long billingEndTime;

    /**
     * 数据创建时间戳
     */
    @TableField("create_time")
    @Schema(description = "数据创建时间戳", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long createTime;

    /**
     * 是否删除
     */
    @TableField("deleted")
    @Schema(description = "是否删除")
    private Integer deleted;

    /**
     * 服务优惠详情json 见 List<com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.Coupon>
     */
    @Schema(description = "服务优惠详情json 见 List<com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.Coupon>")
    @TableField("discount_details")
    private String discountDetails;


    /**
     * 目录价(原价)
     */
    @TableField("original_price")
    @Schema(description = "目录价(原价)")
    private BigDecimal originalPrice;

    /**
     * 钱包扣款状态（0-未扣款、1-成功、2-失败、3-余额不足, 4-未结清）
     */
    @TableField("wallet_deduct_status")
    @Schema(description = "钱包扣款状态（0-未扣款、1-成功、2-失败、3-余额不足, 4-未结清）")
    private Integer walletDeductStatus;

    /**
     * 已支付金额
     */
    @TableField("paid_amount")
    @Schema(description = "已支付金额")
    private BigDecimal paidAmount;

    /**
     * 未支付金额
     */
    @TableField("unpaid_amount")
    @Schema(description = "未支付金额")
    private BigDecimal unpaidAmount;

}