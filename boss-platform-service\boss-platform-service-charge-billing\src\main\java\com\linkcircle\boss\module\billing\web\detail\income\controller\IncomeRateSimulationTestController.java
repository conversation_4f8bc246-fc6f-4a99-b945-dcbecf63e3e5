package com.linkcircle.boss.module.billing.web.detail.income.controller;

import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.module.billing.web.detail.income.scheduled.IncomeRateSimulationScheduledTask;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2025-01-11 15:00
 * @description 收入费率模拟定时任务测试控制器
 */
@Tag(name = "收入费率模拟测试")
@RestController
@RequestMapping("/billing/income-rate-simulation/test")
@RequiredArgsConstructor
public class IncomeRateSimulationTestController {

    private final IncomeRateSimulationScheduledTask incomeRateSimulationScheduledTask;

    @PostMapping("/fixed-rate")
    @Operation(summary = "手动触发固定费率模拟详单发送")
    public CommonResult<String> triggerFixedRateSimulation() {
        incomeRateSimulationScheduledTask.incomeFixedRateSimulationHandler();
        return CommonResult.success("固定费率模拟详单发送任务执行完成");
    }

    @PostMapping("/tiered-rate")
    @Operation(summary = "手动触发阶梯费率模拟详单发送")
    public CommonResult<String> triggerTieredRateSimulation() {
        incomeRateSimulationScheduledTask.incomeTieredRateSimulationHandler();
        return CommonResult.success("阶梯费率模拟详单发送任务执行完成");
    }
}