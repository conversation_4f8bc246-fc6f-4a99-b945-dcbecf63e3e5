package com.linkcircle.boss.module.charge.fee.web.invoice.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linkcircle.boss.module.charge.fee.web.invoice.model.entity.ChargeInvoiceDO;
import com.linkcircle.boss.module.charge.fee.web.invoiceRecord.model.dto.InvoiceRecordReqDTO;
import com.linkcircle.boss.module.charge.fee.web.invoiceRecord.model.vo.InvoiceRecordResVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/30 10:07
 */
@Mapper
public interface InvoiceMapper extends BaseMapper<ChargeInvoiceDO> {

    /**
     * 发票记录分页查询
     *
     * @param page
     * @param queryDTO
     * @return
     */
    List<InvoiceRecordResVO> queryByPage(@Param("page") Page<?> page,
                                         @Param("query") InvoiceRecordReqDTO queryDTO);

    default long   checkInvoiceIdDuplicate(String invoiceId){
        LambdaQueryWrapper<ChargeInvoiceDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChargeInvoiceDO::getInvoiceId, invoiceId);
        return this.selectCount(queryWrapper);
    }
}
