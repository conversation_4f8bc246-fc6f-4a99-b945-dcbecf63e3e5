package com.linkcircle.boss.module.charge.crm.web.plan.controller;

import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.framework.common.model.PageResult;
import com.linkcircle.boss.module.charge.crm.web.plan.model.dto.ChargePlanCreateReqDTO;
import com.linkcircle.boss.module.charge.crm.web.plan.model.dto.ChargePlanPageReqDTO;
import com.linkcircle.boss.module.charge.crm.web.plan.model.dto.ChargePlanSelectDTO;
import com.linkcircle.boss.module.charge.crm.web.plan.model.entity.ChargePlanDO;
import com.linkcircle.boss.module.charge.crm.web.plan.model.vo.ChargePlanDetailVO;
import com.linkcircle.boss.module.charge.crm.web.plan.model.vo.ChargePlanPageVO;
import com.linkcircle.boss.module.charge.crm.web.plan.service.ChargePlanService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 计费计划管理 Controller
 *
 * <AUTHOR>
 */
@Tag(name = "计费计划管理")
@RestController
@RequestMapping("/plan")
@Validated
@RequiredArgsConstructor
public class ChargePlanController {

    private final ChargePlanService chargePlanService;

    @GetMapping("/page")
    @Operation(method = "GET", summary = "计费计划分页查询")
    public CommonResult<PageResult<ChargePlanPageVO>> pageQuery(@ParameterObject @Valid ChargePlanPageReqDTO dto) {
        PageResult<ChargePlanPageVO> pageResult = chargePlanService.page(dto);
        return CommonResult.success(pageResult);
    }

    @GetMapping("/queryAll")
    @Operation(method = "GET", summary = "计划下拉框")
    public CommonResult<?> queryAll(@ParameterObject ChargePlanSelectDTO dto) {
        List<ChargePlanDO> list = chargePlanService.queryAll(dto);
        return CommonResult.success(list);
    }

    @GetMapping("/detail")
    @Operation(summary = "获取计划详情")
    public CommonResult<ChargePlanDetailVO> detail(@RequestParam Long id) {
        ChargePlanDetailVO result = chargePlanService.detail(id);
        return CommonResult.success(result);
    }

    @PostMapping("/create")
    @Operation(summary = "创建计划")
    public CommonResult<Long> create(@RequestBody @Valid ChargePlanCreateReqDTO dto) {
        return CommonResult.success(chargePlanService.create(dto));
    }

    @PostMapping("/update")
    @Operation(summary = "更新计划")
    public CommonResult<Boolean> update(@RequestBody @Valid ChargePlanCreateReqDTO dto) {
        return CommonResult.success(chargePlanService.update(dto));
    }

    @GetMapping("/change-status")
    @Operation(summary = "变更计划状态 0-未激活 1-激活 2-存档")
    public CommonResult<Boolean> changeStatus(@RequestParam Long id,
                                              @RequestParam Integer status) {
        return CommonResult.success(chargePlanService.changeStatus(id, status));
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除计费计划")
    public CommonResult<Boolean> delete(@RequestParam Long id) {
        return CommonResult.success(chargePlanService.delete(id));
    }

    @GetMapping("/copy")
    @Operation(summary = "复制计划")
    public CommonResult<Boolean> copy(@RequestParam Long id) {
        chargePlanService.copy(id);
        return CommonResult.success();
    }
}