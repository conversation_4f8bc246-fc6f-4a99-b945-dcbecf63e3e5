<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.linkcircle.boss.module.charge.crm.web.resourceservice.mapper.ChargeResourceServiceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap"
               type="com.linkcircle.boss.module.charge.crm.web.resourceservice.model.entity.ChargeResourceService">
        <id column="id" property="id"/>
        <result column="service_name" property="serviceName"/>
        <result column="service_code" property="serviceCode"/>
        <result column="scale_id" property="scaleId"/>
        <result column="config_bus_proto" property="configBusProto"/>
        <result column="event_name" property="eventName"/>
        <result column="status" property="status"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="creator" property="creator"/>
        <result column="updater" property="updater"/>
        <result column="deleted" property="deleted"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , service_name,service_code, scale_id,config_bus_proto, event_name, `status`, tenant_id, create_time, update_time, creator, updater, deleted
    </sql>
    <select id="pageQuery"
            resultType="com.linkcircle.boss.module.charge.crm.web.resourceservice.model.vo.ChargeResourceServiceVO">
        SELECT
        crs.id AS serviceId,
        crs.service_name AS serviceName,
        crs.invoice_shows AS invoiceShows,
        crs.charge_type AS chargeType,
        crs.status AS status,
        crs.scale_id AS scaleId,
        crs.config_bus_proto configBusProto,
        crs.creator AS creator,
        crs.create_time AS createTime,
        crs.update_time AS updateTime,
        crsp.description AS description,
        crsp.unit_label AS unitLabel,
        crsp.version_name as versionName,
        crsp.payment_options AS paymentOptions,
        crsp.currency_collection AS currencyCollection,
        crsp.currency_price_json AS currencyPriceJson,
        crsp.in_package AS inPackage,
        crsp.period AS period,
        crsp.unit_period AS unit_period
        FROM charge_resource_service crs
        LEFT JOIN charge_resource_service_price crsp ON crsp.service_id = crs.id
        <where>
            crs.deleted = 0
            AND crsp.version_code = (
            SELECT MAX(sub.version_code)
            FROM charge_resource_service_price sub
            WHERE sub.service_id = crsp.service_id
            and sub.deleted = 0
            )
            <if test="query.serviceName != null and query.serviceName != ''">
                AND crs.service_name like CONCAT('%',#{query.serviceName},'%')
            </if>
            <if test="query.status != null">
                AND crs.status =#{query.status}
            </if>
        </where>
    </select>
    <select id="getVersionList"
            resultType="com.linkcircle.boss.module.charge.crm.web.resourceservice.model.vo.ChargeResourceServiceVersionVO">
        select
        id versionId,
        version_name versionName
        from charge_resource_service_price
        <where>
            deleted = 0
            and service_id=#{serviceId}
        </where>
        order by version_code desc
    </select>
    <select id="getVersionInfoById"
            resultType="com.linkcircle.boss.module.charge.crm.web.resourceservice.model.vo.ChargeResourceServiceVersionInfoVO">
        select crsp.id versionId,
        crs.service_name serviceName,
        crs.invoice_shows invoiceShows,
        crs.charge_type chargeType,
        crs.scale_id scaleId,
        crs.event_name eventName,
        crs.id resourceServiceId,
        crs.status status,
        crsp.description description,
        crsp.version_code versionCode,
        crsp.version_name versionName,
        crsp.unit_label unitLabel,
        crsp.payment_options paymentOptions,
        crsp.currency_collection currencyCollection,
        crsp.currency_price_json currencyPriceJson,
        crsp.in_package inPackage,
        crsp.period period,
        crsp.unit_period unit_period
        from charge_resource_service_price crsp
        left join charge_resource_service crs on crsp.service_id = crs.id
        <where>
            crsp.id IN (
            <if test="versionId != null">#{versionId}</if>
            <if test="idList != null and !idList.isEmpty()">
                <foreach collection="idList" item="id" separator=",">#{id}</foreach>
            </if>
            )
        </where>
    </select>
    <select id="getMaxVersionInfoById"
            resultType="com.linkcircle.boss.module.charge.crm.web.resourceservice.model.vo.ChargeResourceServiceVersionInfoVO">
        SELECT crsp.id                  AS versionId,
               crs.service_name         AS serviceName,
               crs.invoice_shows        AS invoiceShows,
               crs.charge_type          AS chargeType,
               crs.scale_id             AS scaleId,
               crs.event_name           AS eventName,
               crsp.description         AS description,
               crsp.version_code        AS versionCode,
               crsp.version_name        AS versionName,
               crsp.unit_label          AS unitLabel,
               crsp.payment_options     AS paymentOptions,
               crsp.currency_collection AS currencyCollection,
               crsp.currency_price_json AS currencyPriceJson,
               crsp.in_package          AS inPackage,
               crsp.period              AS period,
               crsp.unit_period         AS unit_period
        FROM charge_resource_service_price crsp
                 LEFT JOIN charge_resource_service crs ON crsp.service_id = crs.id
        WHERE crsp.service_id = #{serviceId}
          AND crsp.version_code = (SELECT MAX(sub.version_code)
                                   FROM charge_resource_service_price sub
                                   WHERE sub.service_id = crsp.service_id)
            LIMIT 1;
    </select>
    <select id="getVersionInfoListByParam"
            resultType="com.linkcircle.boss.module.charge.crm.web.resourceservice.model.vo.ChargeResourceServiceVersionInfoVO">
        SELECT
        crs.id AS resourceServiceId,
        crs.service_name AS serviceName,
        crs.invoice_shows AS invoiceShows,
        crs.charge_type AS chargeType,
        csi.supplier_name AS supplierName,
        crsp.id As versionId,
        crsp.description AS description,
        crsp.unit_label AS unitLabel,
        crsp.version_name as versionName,
        crsp.version_code as versionCode,
        crsp.payment_options AS paymentOptions,
        crsp.currency_collection AS currencyCollection,
        crsp.currency_price_json AS currencyPriceJson,
        crsp.in_package AS inPackage,
        crsp.period AS period,
        crsp.unit_period AS unit_period
        FROM charge_resource_service crs
        LEFT JOIN charge_resource_service_price crsp ON crsp.service_id = crs.id
        LEFT JOIN charge_supplier_info csi ON FIND_IN_SET(crsp.id,csi.resource_ids)>0
        <where>
            crs.deleted = 0
            and crs.status = 1
            <if test="param.serviceName != null and param.serviceName != ''">
                AND crs.service_name like CONCAT('%',#{param.serviceName},'%')
            </if>
            <if test="param.currencyCode != null and param.currencyCode != ''">
                AND FIND_IN_SET(#{param.currencyCode},crsp.currency_collection)
            </if>

        </where>
        ORDER BY crs.id, crsp.version_code;
    </select>

</mapper>
