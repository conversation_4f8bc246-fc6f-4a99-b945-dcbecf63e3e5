package com.linkcircle.boss.module.charge.fee.web.bill.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.linkcircle.boss.module.billing.api.bill.product.model.entity.PostpaidProductIncomeBillDO;
import com.linkcircle.boss.module.billing.api.detail.income.model.entity.PrepaidIncomeBillDetailDO;
import com.linkcircle.boss.module.charge.fee.enums.InvoiceEnum;
import com.linkcircle.boss.module.charge.fee.web.bill.model.entity.MakeupIncomeBillDO;
import com.linkcircle.boss.module.charge.fee.web.bill.model.vo.postpaid.PostpaidIncomeProductBillDetailVO;
import com.linkcircle.boss.module.charge.fee.web.invoice.model.dto.BillDetailReqDTO;
import com.linkcircle.boss.module.charge.fee.web.invoice.model.dto.BillInvoiceCheckGroupDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @date 2025/6/25 14:50
 */
@Mapper
public interface PostpaidMapper extends BaseMapper<PostpaidProductIncomeBillDO> {

    public List<BillInvoiceCheckGroupDTO> checkByIds(@Param("detailIds") List<Long> detailIds,@Param("startTime") Long startTime,     @Param("endTime") Long endTime);

    public List<BillInvoiceCheckGroupDTO> checkByDetail(@Param("req") BillDetailReqDTO reqDTO);


    default List<PostpaidProductIncomeBillDO> queryBillByIds(List<Long> detailIds, Long startTime, Long endTime){
        LambdaQueryWrapper<PostpaidProductIncomeBillDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(PostpaidProductIncomeBillDO::getProductBillId, detailIds);
        wrapper.ge(PostpaidProductIncomeBillDO::getBillingTime, startTime);
        wrapper.le(PostpaidProductIncomeBillDO::getBillingTime, endTime);
        wrapper.eq(PostpaidProductIncomeBillDO::getDeleted, false);
        return selectList(wrapper);
    }

    //PostpaidProductIncomeBillDO selectByIdAndTime(Long billId, Long billingTime);


    default void updateStatus(Long billId, Long billingTime, InvoiceEnum.BillStatus billStatus) {

        LambdaUpdateWrapper<PostpaidProductIncomeBillDO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(PostpaidProductIncomeBillDO::getBillStatus, billStatus.getCode());
        wrapper.eq(PostpaidProductIncomeBillDO::getProductBillId, billId);
        wrapper.eq(PostpaidProductIncomeBillDO::getBillingTime, billingTime);
        update(wrapper);

    }



    default int   updateByIdAndTime(PostpaidProductIncomeBillDO bill){
        LambdaUpdateWrapper<PostpaidProductIncomeBillDO> wrapper = new LambdaUpdateWrapper<>();
        Long time = bill.getBillingTime();
        bill.setBillingTime(null);
        wrapper.eq(PostpaidProductIncomeBillDO::getProductBillId,bill.getProductBillId())
                .eq(PostpaidProductIncomeBillDO::getBillingTime,time);
        update(bill,wrapper);
        bill.setBillingTime(time);
        return 0;
    };

    default int  deleteByProduct(PostpaidIncomeProductBillDetailVO productBillDetailVO){
        LambdaUpdateWrapper<PostpaidProductIncomeBillDO> wrapper = new LambdaUpdateWrapper<>();
        Long time = productBillDetailVO.getBillingTime();
        productBillDetailVO.setBillingTime(null);
        wrapper.eq(PostpaidProductIncomeBillDO::getProductBillId,productBillDetailVO.getProductBillId())
                .eq(PostpaidProductIncomeBillDO::getBillingTime,time);
        wrapper.set(PostpaidProductIncomeBillDO::getDeleted,true);
        update(wrapper);
        productBillDetailVO.setBillingTime(time);
        return 0;
    }

    default boolean checkBillCodeDuplicate(String billCode){
        LambdaQueryWrapper<PostpaidProductIncomeBillDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PostpaidProductIncomeBillDO::getBillNo,billCode);
        return selectCount(wrapper)>0;
    }

    /**
     * 统计指定客户的未支付账单数量
     *
     * @param customerId 客户ID
     * @return 未支付账单数量
     */
    default long countUnPaidBill(Long customerId){
        LambdaQueryWrapper<PostpaidProductIncomeBillDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PostpaidProductIncomeBillDO::getCustomerId,customerId);
        wrapper.eq(PostpaidProductIncomeBillDO::getDeleted,false);
        wrapper.ne(PostpaidProductIncomeBillDO::getBillStatus, InvoiceEnum.BillStatus.PAID.getCode());
        return selectCount(wrapper);
    }

    default BigDecimal calculateBillFee(Long constructId, Long startTime, Long endTime) {
        QueryWrapper<PostpaidProductIncomeBillDO> wrapper = new QueryWrapper<>();
        wrapper.select("sum(amountWithTax) as billFee")
                .eq("contract_id", constructId)
                .eq("deleted", false);
        if(startTime != null) {
            wrapper.ge("billing_time", startTime);
        }
        if(endTime != null) {
            wrapper.le("billing_time", endTime);
        }
        wrapper.groupBy("contract_id");
        List<Map<String, Object>> maps = selectMaps(wrapper);
        BigDecimal billFee = null;
        if(!maps.isEmpty()) {
            for (Map<String, Object> map : maps) {
                billFee = (BigDecimal) map.get("billFee");
            }
        }
        return billFee==null?BigDecimal.ZERO:billFee;
    }

    default  int refundInvoice(Long billId, Long billingTime, BigDecimal finalRefundAmount){
        LambdaUpdateWrapper<PostpaidProductIncomeBillDO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(PostpaidProductIncomeBillDO::getProductBillId,billId)
                .eq(PostpaidProductIncomeBillDO::getBillingTime,billingTime);
        wrapper.set(PostpaidProductIncomeBillDO::getRefundInvoiceAmount,finalRefundAmount);
        return update(wrapper);
    }
}
