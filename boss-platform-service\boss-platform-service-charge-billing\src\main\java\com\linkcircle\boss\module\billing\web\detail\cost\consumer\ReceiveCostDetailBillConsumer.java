package com.linkcircle.boss.module.billing.web.detail.cost.consumer;

import com.linkcircle.boss.framework.common.constants.ChargeTopicConstant;
import com.linkcircle.boss.framework.common.exception.IdempotentException;
import com.linkcircle.boss.framework.common.util.json.JsonUtils;
import com.linkcircle.boss.framework.tanant.TenantContextHolder;
import com.linkcircle.boss.framework.web.context.EnableLoginContext;
import com.linkcircle.boss.module.billing.constants.ResponsibilityChainGroupConstant;
import com.linkcircle.boss.module.billing.exception.RepeatedBillingException;
import com.linkcircle.boss.module.billing.exception.ServiceConfigNotFoundException;
import com.linkcircle.boss.module.billing.web.detail.cost.model.dto.ReceiveCostBillMqDTO;
import com.linkcircle.boss.module.billing.web.detail.cost.service.calculation.chain.context.CostDetailBillErrorEnum;
import com.linkcircle.boss.module.billing.web.detail.cost.service.calculation.chain.context.CostDetailBillRequestContext;
import com.linkcircle.boss.module.billing.web.idempotent.service.IdempotentService;
import com.linkcircle.boss.module.crm.enums.BillTypeEnum;
import io.github.kk01001.design.pattern.responsibility.ResponsibilityChainContext;
import io.github.kk01001.design.pattern.responsibility.ResponsibilityChainFactory;
import io.github.kk01001.design.pattern.strategy.exception.StrategyException;
import io.github.kk01001.util.TraceIdUtil;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.redisson.api.RLock;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import java.time.Duration;

/**
 * <AUTHOR>
 * @date 2025-06-24 15:00
 * @description 成本账单消费者
 */
@Component
@Slf4j
@RequiredArgsConstructor
@RocketMQMessageListener(
        topic = ChargeTopicConstant.CHARGE_NORMAL_TOPIC,
        selectorExpression = ChargeTopicConstant.TAG_RECEIVE_COST_BILL,
        consumerGroup = ChargeTopicConstant.GROUP_RECEIVE_COST_BILL,
        maxReconsumeTimes = 10
)
public class ReceiveCostDetailBillConsumer implements RocketMQListener<ReceiveCostBillMqDTO> {

    private final ResponsibilityChainFactory responsibilityChainFactory;
    private final IdempotentService idempotentService;
    private final TransactionTemplate transactionTemplate;

    @SneakyThrows
    @Override
    public void onMessage(ReceiveCostBillMqDTO message) {
        handler(message);
    }

    public CostDetailBillErrorEnum handler(ReceiveCostBillMqDTO message) {
        try {
            String businessId = message.getRequestParams().getBusinessId();
            String billId = message.getBillId();
            TraceIdUtil.buildAndSetTraceId(" ", billId, businessId);
            log.info("接收到成本账单MQ消息, message: {}", JsonUtils.toJsonString(message));
            EnableLoginContext.setContext(false);
            TenantContextHolder.setTenantId(message.getTenantId());

            if (!idempotentService.tryLockProcess(BillTypeEnum.COST.name(), billId, Duration.ofSeconds(10))) {
                throw new IdempotentException("成本billId重复消费", Long.valueOf(billId));
            }

            // 添加账户锁
            Long accountId = message.getRequestParams().getAccountId();
            RLock lock = idempotentService.lockAccountProcess(accountId);
            try {
                return transactionTemplate.execute(transactionStatus -> {
                    boolean marked = idempotentService.markCostBillProcessed(billId);
                    if (!marked) {
                        log.warn("db billId重复消费, billId: {}", billId);
                        return CostDetailBillErrorEnum.DUPLICATE_BILL_CONSUMING;
                    }

                    try {
                        // 处理成本账单
                        ResponsibilityChainContext<CostDetailBillRequestContext, CostDetailBillErrorEnum> context = buildRequestContext(message);
                        CostDetailBillErrorEnum result = responsibilityChainFactory.execute(ResponsibilityChainGroupConstant.COST_DETAIL_BILL, context);
                        log.info("成本账单处理完成, billId: {}, result: {}", billId, result);
                        return result;
                    } catch (StrategyException e) {
                        String strategyType = e.getStrategyType();
                        log.error("处理成本账单策略异常, message: {}, strategyType: {}, e: ", JsonUtils.toJsonString(message), strategyType, e.getCause());
                        throw new RuntimeException(e.getCause());
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                });
            } catch (Exception e) {
                // 判断异常 有些异常就不重试了 如NPE 数据问题
                log.error("处理成本账单消费异常, message: {}, e: ", JsonUtils.toJsonString(message), e);
                if (!skipException(e)) {
                    throw e;
                }
            } finally {
                idempotentService.unlockAccountProcess(accountId, lock);
            }

        } finally {
            EnableLoginContext.clearContext();
            TenantContextHolder.clear();
            TraceIdUtil.remove();
        }
        return CostDetailBillErrorEnum.SKIP;
    }

    private boolean skipException(Exception e) {
        return e instanceof ServiceConfigNotFoundException
                || e instanceof IdempotentException
                || e instanceof RepeatedBillingException;
    }

    private ResponsibilityChainContext<CostDetailBillRequestContext, CostDetailBillErrorEnum> buildRequestContext(ReceiveCostBillMqDTO message) {
        ResponsibilityChainContext<CostDetailBillRequestContext, CostDetailBillErrorEnum> context = new ResponsibilityChainContext<>();
        CostDetailBillRequestContext requestContext = new CostDetailBillRequestContext();
        requestContext.setCostBillMqDTO(message);
        requestContext.setBillId(message.getBillId());
        context.setData(requestContext);
        return context;
    }
}
