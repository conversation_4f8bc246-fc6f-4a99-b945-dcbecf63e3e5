package com.linkcircle.boss.billing.sdk.builder;

import com.linkcircle.boss.billing.sdk.model.request.CostBillDetailRequest;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025-07-25 15:00
 * @description 成本账单明细请求构建器
 */
public class CostBillDetailRequestBuilder {

    private final CostBillDetailRequest request;

    public CostBillDetailRequestBuilder() {
        this.request = new CostBillDetailRequest();
        this.request.setData(new HashMap<>());
    }

    /**
     * 设置账户ID
     */
    public CostBillDetailRequestBuilder accountId(Long accountId) {
        request.setAccountId(accountId);
        return this;
    }

    /**
     * 设置资源ID
     */
    public CostBillDetailRequestBuilder resourceId(Long resourceId) {
        request.setResourceId(resourceId);
        return this;
    }

    /**
     * 设置资源服务ID
     */
    public CostBillDetailRequestBuilder resourceServiceId(Long resourceServiceId) {
        request.setResourceServiceId(resourceServiceId);
        return this;
    }

    /**
     * 设置业务时间
     */
    public CostBillDetailRequestBuilder businessTime(Long businessTime) {
        request.setBusinessTime(businessTime);
        return this;
    }

    /**
     * 设置消耗量
     */
    public CostBillDetailRequestBuilder usage(BigDecimal usage) {
        request.setUsage(usage);
        return this;
    }

    /**
     * 设置消耗量（数值）
     */
    public CostBillDetailRequestBuilder usage(double usage) {
        request.setUsage(BigDecimal.valueOf(usage));
        return this;
    }

    /**
     * 设置消耗量单位
     */
    public CostBillDetailRequestBuilder usageUnit(String usageUnit) {
        request.setUsageUnit(usageUnit);
        return this;
    }

    /**
     * 设置回调URL
     */
    public CostBillDetailRequestBuilder callbackUrl(String callbackUrl) {
        request.setCallbackUrl(callbackUrl);
        return this;
    }

    /**
     * 设置业务ID
     */
    public CostBillDetailRequestBuilder businessId(String businessId) {
        request.setBusinessId(businessId);
        return this;
    }

    /**
     * 设置请求ID
     */
    public CostBillDetailRequestBuilder requestId(String requestId) {
        request.setRequestId(requestId);
        return this;
    }

    /**
     * 添加业务数据
     */
    public CostBillDetailRequestBuilder addData(String key, Object value) {
        request.getData().put(key, value);
        return this;
    }

    /**
     * 设置业务数据
     */
    public CostBillDetailRequestBuilder data(Map<String, Object> data) {
        request.setData(data);
        return this;
    }

    /**
     * 构建请求对象
     */
    public CostBillDetailRequest build() {
        return request;
    }
}
