package com.linkcircle.boss.billing.sdk.http;

import cn.hutool.core.util.IdUtil;
import com.linkcircle.boss.billing.sdk.config.BillingConfig;
import com.linkcircle.boss.billing.sdk.exception.BillingException;
import com.linkcircle.boss.billing.sdk.model.request.BaseBillDetailRequest;
import com.linkcircle.boss.billing.sdk.model.response.BillingApiResponse;
import com.linkcircle.boss.billing.sdk.util.JsonUtil;
import com.linkcircle.boss.billing.sdk.util.SignUtil;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025-07-25 15:00
 * @description HTTP客户端
 */
public class HttpClient {

    private static final Logger logger = LoggerFactory.getLogger(HttpClient.class);

    private static final MediaType JSON_MEDIA_TYPE = MediaType.get("application/json; charset=utf-8");

    private final BillingConfig config;
    private final OkHttpClient okHttpClient;

    public HttpClient(BillingConfig config) {
        this.config = config;
        this.okHttpClient = buildOkHttpClient();
    }

    /**
     * 构建OkHttpClient
     */
    private OkHttpClient buildOkHttpClient() {
        OkHttpClient.Builder builder = new OkHttpClient.Builder()
                .connectTimeout(config.getConnectTimeout(), TimeUnit.MILLISECONDS)
                .readTimeout(config.getReadTimeout(), TimeUnit.MILLISECONDS)
                .writeTimeout(config.getWriteTimeout(), TimeUnit.MILLISECONDS)
                .retryOnConnectionFailure(true);

        if (config.isDebug()) {
            builder.addInterceptor(new HttpLoggingInterceptor(logger::info)
                    .setLevel(HttpLoggingInterceptor.Level.BODY));
        }

        return builder.build();
    }

    /**
     * 发送POST请求
     *
     * @param url           请求URL
     * @param request       请求对象
     * @param responseClass 响应类型
     * @param <T>           响应泛型
     * @return 响应结果
     */
    public <T> BillingApiResponse<T> post(String url, BaseBillDetailRequest request, Class<T> responseClass) {
        // 1. 预处理请求参数
        preprocessRequest(request);

        // 2. 转换为Map并生成签名
        Map<String, Object> paramMap = JsonUtil.objectToMap(request);
        String sign = SignUtil.generateSign(paramMap, config.getAppSecret());
        request.setSign(sign);

        // 3. 构建HTTP请求
        String requestBody = JsonUtil.toJsonString(request);
        Request httpRequest = new Request.Builder()
                .url(url)
                .addHeader("Content-Type", "application/json")
                .addHeader("X-AppId", config.getAppId())
                .addHeader("X-Tenant", String.valueOf(config.getTenantId()))
                .post(RequestBody.create(requestBody, JSON_MEDIA_TYPE))
                .build();

        // 4. 执行请求
        return executeRequest(httpRequest, responseClass);
    }

    /**
     * 预处理请求参数
     */
    private void preprocessRequest(BaseBillDetailRequest request) {
        // 设置时间戳
        if (request.getTimestamp() == null) {
            request.setTimestamp(System.currentTimeMillis());
        }

        // 生成请求ID
        if (request.getRequestId() == null || request.getRequestId().trim().isEmpty()) {
            request.setRequestId(IdUtil.fastSimpleUUID());
        }

        // 验证必要参数
        validateRequest(request);
    }

    /**
     * 验证请求参数
     */
    private void validateRequest(BaseBillDetailRequest request) {
        if (request.getAccountId() == null) {
            throw new BillingException("PARAM_ERROR", "账户ID不能为空");
        }
        if (request.getBusinessTime() == null) {
            throw new BillingException("PARAM_ERROR", "业务时间不能为空");
        }
        if (request.getUsage() == null) {
            throw new BillingException("PARAM_ERROR", "消耗量不能为空");
        }
        if (request.getUsageUnit() == null || request.getUsageUnit().trim().isEmpty()) {
            throw new BillingException("PARAM_ERROR", "消耗量单位不能为空");
        }
        if (request.getCallbackUrl() == null || request.getCallbackUrl().trim().isEmpty()) {
            throw new BillingException("PARAM_ERROR", "回调URL不能为空");
        }
        if (request.getBusinessId() == null || request.getBusinessId().trim().isEmpty()) {
            throw new BillingException("PARAM_ERROR", "业务ID不能为空");
        }
        if (request.getData() == null) {
            throw new BillingException("PARAM_ERROR", "业务数据不能为空");
        }
    }

    /**
     * 执行HTTP请求
     */
    private <T> BillingApiResponse<T> executeRequest(Request request, Class<T> responseClass) {
        int retryCount = 0;
        Exception lastException = null;

        while (retryCount <= config.getMaxRetries()) {
            try {
                if (config.isDebug()) {
                    logger.info("发送请求: {}, 重试次数: {}", request.url(), retryCount);
                }

                Response response = okHttpClient.newCall(request).execute();
                return handleResponse(response, responseClass);

            } catch (Exception e) {
                lastException = e;
                retryCount++;

                if (retryCount <= config.getMaxRetries()) {
                    logger.warn("请求失败，准备重试: {}/{}, 错误: {}", retryCount, config.getMaxRetries(), e.getMessage());
                    try {
                        Thread.sleep(1000 * retryCount); // 递增延迟
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }

        throw new BillingException("HTTP_ERROR", "请求失败，已重试" + config.getMaxRetries() + "次", lastException);
    }

    /**
     * 处理HTTP响应
     */
    private <T> BillingApiResponse<T> handleResponse(Response response, Class<T> responseClass) throws IOException {
        String responseBody = null;
        try {
            ResponseBody body = response.body();
            if (body == null) {
                throw new BillingException("HTTP_ERROR", "响应体为空");
            }

            responseBody = body.string();

            if (config.isDebug()) {
                logger.info("收到响应: status={}, body={}", response.code(), responseBody);
            }

            if (!response.isSuccessful()) {
                throw new BillingException("HTTP_ERROR", "HTTP请求失败: " + response.code() + ", " + responseBody);
            }

            // 解析响应
            BillingApiResponse<T> apiResponse = JsonUtil.parseObject(responseBody, BillingApiResponse.class);
            
            // 如果有数据且需要转换类型
            if (apiResponse.getData() != null && responseClass != Object.class) {
                Object data = apiResponse.getData();
                T convertedData = JsonUtil.mapToObject((Map<String, Object>) data, responseClass);
                apiResponse.setData(convertedData);
            }

            return apiResponse;

        } catch (Exception e) {
            logger.error("解析响应失败: {}", responseBody, e);
            throw new BillingException("PARSE_ERROR", "解析响应失败: " + e.getMessage(), e);
        }
    }

    /**
     * 关闭客户端
     */
    public void close() {
        if (okHttpClient != null) {
            okHttpClient.dispatcher().executorService().shutdown();
            okHttpClient.connectionPool().evictAll();
        }
    }
}
