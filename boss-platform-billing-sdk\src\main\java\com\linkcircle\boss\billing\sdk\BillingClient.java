package com.linkcircle.boss.billing.sdk;

import com.linkcircle.boss.billing.sdk.config.BillingConfig;
import com.linkcircle.boss.billing.sdk.exception.BillingException;
import com.linkcircle.boss.billing.sdk.http.HttpClient;
import com.linkcircle.boss.billing.sdk.model.request.CostBillDetailRequest;
import com.linkcircle.boss.billing.sdk.model.request.IncomeBillDetailRequest;
import com.linkcircle.boss.billing.sdk.model.response.BillingApiResponse;
import com.linkcircle.boss.billing.sdk.model.response.CostBillDetailResponse;
import com.linkcircle.boss.billing.sdk.model.response.IncomeBillDetailResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @date 2025-07-25 15:00
 * @description 计费平台SDK客户端
 */
public class BillingClient {

    private static final Logger logger = LoggerFactory.getLogger(BillingClient.class);

    /**
     * 收入账单明细接口路径
     */
    private static final String INCOME_BILL_DETAIL_PATH = "/billing/api/v3/income/charge-income-bill-detail";

    /**
     * 成本账单明细接口路径
     */
    private static final String COST_BILL_DETAIL_PATH = "/billing/api/v3/cost/charge-cost-bill-detail";

    private final BillingConfig config;
    private final HttpClient httpClient;

    /**
     * 构造函数
     *
     * @param config 配置信息
     */
    public BillingClient(BillingConfig config) {
        this.config = config;
        this.httpClient = new HttpClient(config);
        logger.info("计费SDK客户端初始化完成, baseUrl: {}, appId: {}", config.getBaseUrl(), config.getAppId());
    }

    /**
     * 推送收入账单明细
     *
     * @param request 收入账单明细请求
     * @return 收入账单明细响应
     * @throws BillingException 计费异常
     */
    public IncomeBillDetailResponse pushIncomeBillDetail(IncomeBillDetailRequest request) throws BillingException {
        if (request == null) {
            throw new BillingException("PARAM_ERROR", "请求参数不能为空");
        }

        // 验证收入账单特有参数
        if (request.getProductId() == null) {
            throw new BillingException("PARAM_ERROR", "产品ID不能为空");
        }
        if (request.getServiceId() == null) {
            throw new BillingException("PARAM_ERROR", "服务ID不能为空");
        }

        String url = buildUrl(INCOME_BILL_DETAIL_PATH);
        logger.info("推送收入账单明细, url: {}, businessId: {}, accountId: {}", 
                url, request.getBusinessId(), request.getAccountId());

        BillingApiResponse<IncomeBillDetailResponse> response = httpClient.post(url, request, IncomeBillDetailResponse.class);

        if (!response.isSuccess()) {
            throw new BillingException("API_ERROR", "推送收入账单明细失败: " + response.getMsg());
        }

        logger.info("推送收入账单明细成功, billDetailId: {}", response.getData().getBillDetailId());
        return response.getData();
    }

    /**
     * 推送成本账单明细
     *
     * @param request 成本账单明细请求
     * @return 成本账单明细响应
     * @throws BillingException 计费异常
     */
    public CostBillDetailResponse pushCostBillDetail(CostBillDetailRequest request) throws BillingException {
        if (request == null) {
            throw new BillingException("PARAM_ERROR", "请求参数不能为空");
        }

        // 验证成本账单特有参数
        if (request.getResourceId() == null) {
            throw new BillingException("PARAM_ERROR", "资源ID不能为空");
        }
        if (request.getResourceServiceId() == null) {
            throw new BillingException("PARAM_ERROR", "资源服务ID不能为空");
        }

        String url = buildUrl(COST_BILL_DETAIL_PATH);
        logger.info("推送成本账单明细, url: {}, businessId: {}, accountId: {}", 
                url, request.getBusinessId(), request.getAccountId());

        BillingApiResponse<CostBillDetailResponse> response = httpClient.post(url, request, CostBillDetailResponse.class);

        if (!response.isSuccess()) {
            throw new BillingException("API_ERROR", "推送成本账单明细失败: " + response.getMsg());
        }

        logger.info("推送成本账单明细成功, billDetailId: {}", response.getData().getBillDetailId());
        return response.getData();
    }

    /**
     * 构建完整URL
     *
     * @param path 接口路径
     * @return 完整URL
     */
    private String buildUrl(String path) {
        String baseUrl = config.getBaseUrl();
        if (baseUrl.endsWith("/")) {
            baseUrl = baseUrl.substring(0, baseUrl.length() - 1);
        }
        return baseUrl + path;
    }

    /**
     * 关闭客户端
     */
    public void close() {
        if (httpClient != null) {
            httpClient.close();
        }
        logger.info("计费SDK客户端已关闭");
    }

    /**
     * 获取配置信息
     *
     * @return 配置信息
     */
    public BillingConfig getConfig() {
        return config;
    }
}
