package com.linkcircle.boss.module.charge.fee.web.bill.service.impl.generator;

import com.linkcircle.boss.module.charge.fee.web.bill.chain.EnhanceHandler;
import com.linkcircle.boss.module.crm.api.basicConfig.vo.InvoiceDetailsVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @date 2025/7/23 17:10
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class YearMonthDayHandler extends EnhanceHandler<InvoiceDetailsVO, String> {
    /**
     * 处理发票详情信息并返回处理后的字符串
     *
     * @param invoiceDetailsVO 发票详情信息对象
     * @param message 需要处理的字符串
     * @return 处理后的字符串
     */
    @Override
    protected String doHandle(InvoiceDetailsVO invoiceDetailsVO, String message) {
        if (message == null) {
            message = invoiceDetailsVO.getNumberFormat();
        }
        if (StringUtils.isNotBlank(message)) {
            LocalDate now = LocalDate.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            String datePart = now.format(formatter);
            message = message.replace("{YYYY}", datePart.substring(0, 4));
            message = message.replace("{YY}", datePart.substring(2, 4));
            message = message.replace("{MM}", datePart.substring(4, 6));
            message = message.replace("{DD}", datePart.substring(6, 8));
        }
        return message;
    }
}
