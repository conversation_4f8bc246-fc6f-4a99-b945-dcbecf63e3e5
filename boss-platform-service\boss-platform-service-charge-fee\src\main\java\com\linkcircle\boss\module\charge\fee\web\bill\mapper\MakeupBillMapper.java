package com.linkcircle.boss.module.charge.fee.web.bill.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.linkcircle.boss.module.billing.api.bill.product.model.entity.PostpaidProductIncomeBillDO;
import com.linkcircle.boss.module.charge.fee.web.bill.model.entity.MakeupIncomeBillDO;
import com.linkcircle.boss.module.charge.fee.enums.InvoiceEnum;
import com.linkcircle.boss.module.charge.fee.web.bill.model.entity.MakeupProductServiceIncomeBillDO;
import com.linkcircle.boss.module.charge.fee.web.bill.model.vo.makeup.MakeupIncomeBillDetailVo;
import com.linkcircle.boss.module.charge.fee.web.bill.model.vo.makeup.MakeupIncomeProductServiceBillDetailVo;
import com.linkcircle.boss.module.charge.fee.web.invoice.model.dto.BillDetailReqDTO;
import com.linkcircle.boss.module.charge.fee.web.invoice.model.dto.BillInvoiceCheckGroupDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2025/6/25 14:50
 */
@Mapper
public interface MakeupBillMapper extends BaseMapper<MakeupIncomeBillDO> {


    default List<MakeupIncomeBillDO> queryBillByIds(List<Long> detailIds, Long startTime, Long endTime) {
        LambdaQueryWrapper<MakeupIncomeBillDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(MakeupIncomeBillDO::getBillId, detailIds);
        wrapper.ge(MakeupIncomeBillDO::getBillingTime, startTime);
        wrapper.le(MakeupIncomeBillDO::getBillingTime, endTime);
        wrapper.eq(MakeupIncomeBillDO::getDeleted, false);
        return selectList(wrapper);
    }

    ;

    default MakeupIncomeBillDO selectByIdAndTime(Long billId, Long billingTime) {
        LambdaQueryWrapper<MakeupIncomeBillDO> wrapper = new LambdaQueryWrapper<MakeupIncomeBillDO>();
        wrapper.eq(MakeupIncomeBillDO::getBillId, billId)
                .eq(MakeupIncomeBillDO::getBillingTime, billingTime);
        return selectOne(wrapper);
    }


    default void updateStatus(Long billId, Long billingTime, InvoiceEnum.BillStatus billStatus) {

        LambdaUpdateWrapper<MakeupIncomeBillDO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(MakeupIncomeBillDO::getBillStatus, billStatus.getCode());
        wrapper.eq(MakeupIncomeBillDO::getBillId, billId);
        wrapper.eq(MakeupIncomeBillDO::getBillingTime, billingTime);
        update(wrapper);

    }

    public List<BillInvoiceCheckGroupDTO> checkByIds(@Param("detailIds") List<Long> detailIds, @Param("startTime") Long startTime, @Param("endTime") Long endTime);

    public List<BillInvoiceCheckGroupDTO> checkByDetail(@Param("req") BillDetailReqDTO reqDTO);

    default int   updateByIdAndTime(MakeupIncomeBillDO bill){
        LambdaUpdateWrapper<MakeupIncomeBillDO> wrapper = new LambdaUpdateWrapper<>();
        Long time = bill.getBillingTime();
        bill.setBillingTime(null);
        wrapper.eq(MakeupIncomeBillDO::getBillId,bill.getBillId())
               .eq(MakeupIncomeBillDO::getBillingTime,time);
         update(bill,wrapper);
         bill.setBillingTime(time);
         return 1;
    };



    default void deleteByBill(MakeupIncomeBillDetailVo detailVo) {
        if(detailVo!=null) {
            LambdaUpdateWrapper<MakeupIncomeBillDO> wrapper = new LambdaUpdateWrapper<>();
            wrapper.set(MakeupIncomeBillDO::getDeleted, 0);
            wrapper.eq(MakeupIncomeBillDO::getBillId, detailVo.getBillId());
            wrapper.eq(MakeupIncomeBillDO::getBillingTime, detailVo.getBillingTime());
            update(wrapper);
        }
    }

    default boolean checkBillCodeDuplicate(String billCode){
        LambdaQueryWrapper<MakeupIncomeBillDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MakeupIncomeBillDO::getBillNo,billCode);
        return selectCount(wrapper)>0;
    }

    /**
     * 统计指定客户的未支付账单数量
     *
     * @param customerId 客户ID
     * @return 未支付账单数量
     */
    default long countUnPaidBill(Long customerId){
        LambdaQueryWrapper<MakeupIncomeBillDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MakeupIncomeBillDO::getCustomerId,customerId);
        wrapper.eq(MakeupIncomeBillDO::getDeleted,false);
        wrapper.ne(MakeupIncomeBillDO::getBillStatus, InvoiceEnum.BillStatus.PAID.getCode());
        return selectCount(wrapper);
    }

    default BigDecimal calculateBillFee(Long constructId, Long startTime, Long endTime) {
        QueryWrapper<MakeupIncomeBillDO> wrapper = new QueryWrapper<>();
        wrapper.select("sum(amountWithTax) as billFee")
                .eq("contract_id", constructId)
                .eq("deleted", false);
        if(startTime != null) {
            wrapper.ge("billing_time", startTime);
        }
        if(endTime != null) {
            wrapper.le("billing_time", endTime);
        }
        wrapper.groupBy("contract_id");
        List<Map<String, Object>> maps = selectMaps(wrapper);
        BigDecimal billFee = null;
        if(!maps.isEmpty()) {
            for (Map<String, Object> map : maps) {
                billFee = (BigDecimal) map.get("billFee");
            }
        }
        return billFee==null?BigDecimal.ZERO:billFee;
    }


    default  int refundInvoice(Long billId, Long billingTime, BigDecimal finalRefundAmount){
        LambdaUpdateWrapper<MakeupIncomeBillDO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(MakeupIncomeBillDO::getBillId,billId)
                .eq(MakeupIncomeBillDO::getBillingTime,billingTime);
        wrapper.set(MakeupIncomeBillDO::getRefundInvoiceAmount,finalRefundAmount);
        return update(wrapper);
    }
}
