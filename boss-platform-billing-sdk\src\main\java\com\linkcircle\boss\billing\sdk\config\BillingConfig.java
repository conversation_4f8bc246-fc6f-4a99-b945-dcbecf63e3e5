package com.linkcircle.boss.billing.sdk.config;

/**
 * <AUTHOR>
 * @date 2025-07-25 15:00
 * @description 计费SDK配置类
 */
public class BillingConfig {

    /**
     * 计费平台基础URL
     */
    private String baseUrl;

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 应用密钥
     */
    private String appSecret;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 连接超时时间（毫秒）
     */
    private int connectTimeout = 30000;

    /**
     * 读取超时时间（毫秒）
     */
    private int readTimeout = 60000;

    /**
     * 写入超时时间（毫秒）
     */
    private int writeTimeout = 60000;

    /**
     * 最大重试次数
     */
    private int maxRetries = 3;

    /**
     * 是否启用调试模式
     */
    private boolean debug = false;

    public BillingConfig() {
    }

    public BillingConfig(String baseUrl, String appId, String appSecret, Long tenantId) {
        this.baseUrl = baseUrl;
        this.appId = appId;
        this.appSecret = appSecret;
        this.tenantId = tenantId;
    }

    public String getBaseUrl() {
        return baseUrl;
    }

    public void setBaseUrl(String baseUrl) {
        this.baseUrl = baseUrl;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getAppSecret() {
        return appSecret;
    }

    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }

    public int getWriteTimeout() {
        return writeTimeout;
    }

    public void setWriteTimeout(int writeTimeout) {
        this.writeTimeout = writeTimeout;
    }

    public int getMaxRetries() {
        return maxRetries;
    }

    public void setMaxRetries(int maxRetries) {
        this.maxRetries = maxRetries;
    }

    public boolean isDebug() {
        return debug;
    }

    public void setDebug(boolean debug) {
        this.debug = debug;
    }

    /**
     * 构建器模式
     */
    public static class Builder {
        private BillingConfig config = new BillingConfig();

        public Builder baseUrl(String baseUrl) {
            config.setBaseUrl(baseUrl);
            return this;
        }

        public Builder appId(String appId) {
            config.setAppId(appId);
            return this;
        }

        public Builder appSecret(String appSecret) {
            config.setAppSecret(appSecret);
            return this;
        }

        public Builder tenantId(Long tenantId) {
            config.setTenantId(tenantId);
            return this;
        }

        public Builder connectTimeout(int connectTimeout) {
            config.setConnectTimeout(connectTimeout);
            return this;
        }

        public Builder readTimeout(int readTimeout) {
            config.setReadTimeout(readTimeout);
            return this;
        }

        public Builder writeTimeout(int writeTimeout) {
            config.setWriteTimeout(writeTimeout);
            return this;
        }

        public Builder maxRetries(int maxRetries) {
            config.setMaxRetries(maxRetries);
            return this;
        }

        public Builder debug(boolean debug) {
            config.setDebug(debug);
            return this;
        }

        public BillingConfig build() {
            if (config.getBaseUrl() == null || config.getBaseUrl().trim().isEmpty()) {
                throw new IllegalArgumentException("baseUrl不能为空");
            }
            if (config.getAppId() == null || config.getAppId().trim().isEmpty()) {
                throw new IllegalArgumentException("appId不能为空");
            }
            if (config.getAppSecret() == null || config.getAppSecret().trim().isEmpty()) {
                throw new IllegalArgumentException("appSecret不能为空");
            }
            if (config.getTenantId() == null) {
                throw new IllegalArgumentException("tenantId不能为空");
            }
            return config;
        }
    }

    public static Builder builder() {
        return new Builder();
    }
}
