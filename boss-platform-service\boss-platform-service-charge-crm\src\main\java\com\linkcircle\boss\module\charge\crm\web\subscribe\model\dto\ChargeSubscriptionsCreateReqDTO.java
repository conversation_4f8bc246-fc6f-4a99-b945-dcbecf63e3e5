package com.linkcircle.boss.module.charge.crm.web.subscribe.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 创建/更新订阅请求 DTO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "创建订阅请求")
public class ChargeSubscriptionsCreateReqDTO {

    @Schema(description = "订阅ID（更新时必填）")
    private Long id;

    @NotNull(message = "客户ID不能为空")
    @Schema(description = "客户ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long customerId;

    @NotNull(message = "账户ID不能为空")
    @Schema(description = "账户ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long accountId;

    @NotNull(message = "钱包ID不能为空")
    @Schema(description = "钱包ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long walletsId;

    @NotNull(message = "主体ID不能为空")
    @Schema(description = "主体ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long entityId;

    @Schema(description = "计划ID")
    private Long planId;

    @Schema(description = "合同ID")
    private Long contractId;

    @NotNull(message = "税率不能为空")
    @Schema(description = "税率百分比（示例：9.00）")
    private BigDecimal rate;

    @NotNull(message = "支付类型不能为空")
    @Schema(description = "支付类型，0：预付费，1后付费")
    private Integer paymentType;

    @Schema(description = "支付方式，0:现金，1：积分")
    private Integer paymentOptions;

    @Schema(description = "状态，0：试用中，1：已生效，2：逾期，3：未支付，4：已取消，5：已结束")
    private Integer status;

    @Schema(description = "出账生成草稿，0：否，1：是")
    private Integer billDrafts;

    @Schema(description = "订阅时间详情json'")
    private String subJson;

    @NotNull(message = "订阅详情内容不能为空")
    @Schema(description = "订阅详情内容")
    private List<ChargeSubscriptionsTimeDetailsDTO> subscriptions;
}