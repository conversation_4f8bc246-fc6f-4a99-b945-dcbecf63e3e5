-- PostgreSQL版本的账单表建表SQL

-- 预付费收入账单明细表
drop table "${serviceCode}_prepaid_income_bill_detail_${year}";
CREATE TABLE "${serviceCode}_prepaid_income_bill_detail_${year}"
(
    bill_detail_id           BIGINT         NOT NULL,          -- 收入账单明细id
    business_id              VARCHAR(128)   NOT NULL,          -- 业务唯一id
    business_time            BIGINT         NOT NULL,          -- 业务产生的时间戳
    service_id               BIGINT         NOT NULL,          -- 服务id
    service_code             VARCHAR(128)   NOT NULL,          -- 服务编码
    customer_id              BIGINT         NOT NULL,          -- 客户id
    account_id               BIGINT         NOT NULL,          -- 账户id
    product_id               BIGINT         NOT NULL,          -- 产品id
    entity_id                BIGINT         NOT NULL,          -- 主体id
    contract_id              BIGINT         NULL,              -- 合同id
    wallet_id                BIGINT         NOT NULL,          -- 钱包id
    plan_id                  BIGINT         NULL,              -- 计划id
    subscribe_id             BIGINT         NULL,              -- 订阅id
    discount_id              VARCHAR(128)   NULL,              -- 优惠id
    usage_count              DECIMAL(18, 2) NOT NULL,          -- 消耗量
    usage_unit               VARCHAR(16)    NULL,              -- 消耗量单位
    charge_usage_count       DECIMAL(18, 2) NOT NULL,          -- 计费消耗量(charge_measure*charge_unit_count)
    charge_unit_count        DECIMAL(18, 2) NOT NULL,          -- 实际计费单位数
    charge_measure           DECIMAL(18, 2) NOT NULL,          -- 计量单位
    charge_measure_unit      VARCHAR(16)    NULL,              -- 计费计量单位
    charge_measure_ceil      SMALLINT       NOT NULL,          -- 计量单位是否向上取整 0-不向上取整, 1-向上取整
    payment_method           SMALLINT       NOT NULL,          -- 支付方式 0-现金, 1-积分
    billing_type             SMALLINT       NOT NULL,          -- 计费类型 0-固定费率，1-阶梯费率，2-套餐计费，3-按量计费
    in_trial                 BOOLEAN        NULL,              -- 是否在计费类型试用期内
    bill_status              SMALLINT       NOT NULL,          -- 账单状态 0-草稿, 1-待支付, 2-已支付, 3-未结清
    wallet_deduct_status     SMALLINT       NULL DEFAULT 0,    -- 钱包扣款状态（0-未扣款、1-成功、2-失败、3-处理中, 4-退款）
    point_amount             DECIMAL(18, 0) NULL DEFAULT 0,    -- 积分
    cash_amount              DECIMAL(18, 6) NULL DEFAULT 0,    -- 现金
    tax_rate                 DECIMAL(5, 4)  NULL DEFAULT 0,    -- 税率
    original_unit_price      DECIMAL(18, 6) NULL DEFAULT 0,    -- 目录价(原单价)
    discounted_unit_price    DECIMAL(18, 6) NULL DEFAULT 0,    -- 订阅价(优惠的目录价)
    original_price           DECIMAL(18, 6) NULL DEFAULT 0,    -- 目录总价(原单价)
    discounted_price         DECIMAL(18, 6) NULL DEFAULT 0,    -- 订阅总价(优惠的目录价)
    discount_amount          DECIMAL(18, 6) NULL DEFAULT 0,    -- 优惠的金额
    invoiced_amount          DECIMAL(18, 6) NULL DEFAULT 0,    -- 已开票金额
    available_invoice_amount DECIMAL(18, 6) NULL DEFAULT 0,    -- 可开票金额(=优惠价-已开票金额)
    amount_with_tax          DECIMAL(18, 6) NULL DEFAULT 0,    -- 含税总金额
    amount_without_tax       DECIMAL(18, 6) NULL DEFAULT 0,    -- 不含税金额
    paid_amount              DECIMAL(18, 6) NULL DEFAULT 0,
    unpaid_amount            DECIMAL(18, 6) NULL DEFAULT 0,
    currency                 VARCHAR(3)     NULL,              -- 货币单位 CNY USD
    payment_time             BIGINT         NULL,              -- 实际支付时间戳
    billing_time             BIGINT         NULL,              -- 出账时间戳
    billing_start_time       BIGINT              DEFAULT NULL, -- 出账开始时间戳（毫秒）
    billing_end_time         BIGINT              DEFAULT NULL, -- 出账结束时间戳（毫秒）
    billing_cycle_type       SMALLINT            DEFAULT NULL, -- 间隔时长单位, 0：一次性, 1:日，2：周，3：月，4：季度，5：年
    billing_day              INTEGER             DEFAULT NULL, -- 间隔时长
    billing_cycle            VARCHAR(32)         DEFAULT NULL, -- 出账周期标识（如202507-202509）
    create_time              BIGINT         NULL,              -- 数据创建时间戳
    deleted                  BOOLEAN        NULL,              -- 是否删除
    request_id               VARCHAR(128)   NOT NULL,          -- 接口请求id
    callback_url             VARCHAR(500)        DEFAULT NULL, -- 回调地址，异步通知使用
    callback_status          SMALLINT            DEFAULT 0,    -- 回调状态：0-未回调，1-回调成功，2-回调失败
    extend_data              JSON                DEFAULT NULL, -- 扩展数据JSON，存储特殊业务数据
    rate_details             JSON                DEFAULT NULL, -- 费用详情json
    discount_details         JSON                DEFAULT NULL, -- 服务优惠详情json

    -- 业务字段
    caller_number            VARCHAR(128)   NULL,              -- 主叫号码
    callee_number            VARCHAR(128)   NULL,              -- 被叫号码
    bill_code     varchar(50) ,
    refund_invoice_amount      decimal(18, 6)
    CONSTRAINT pk_prepaid_income_bill_detail PRIMARY KEY (bill_detail_id, business_id)
);

-- 为预付费收入账单明细表创建索引
CREATE INDEX idx_prepaid_income_account_id ON "${serviceCode}_prepaid_income_bill_detail_${year}" (account_id);
CREATE INDEX idx_prepaid_income_customer_id ON "${serviceCode}_prepaid_income_bill_detail_${year}" (customer_id);
CREATE INDEX idx_prepaid_income_product_id ON "${serviceCode}_prepaid_income_bill_detail_${year}" (product_id);
CREATE INDEX idx_prepaid_income_bill_code ON "${serviceCode}_prepaid_income_bill_detail_${year}" (bill_code);
-- 添加表注释
COMMENT ON TABLE "${serviceCode}_prepaid_income_bill_detail_${year}" IS '预付费-收入账单-明细表 ${serviceCode} - ${year}';

-- 添加字段注释
COMMENT ON COLUMN "${serviceCode}_prepaid_income_bill_detail_${year}".bill_detail_id IS '收入账单明细id';
COMMENT ON COLUMN "${serviceCode}_prepaid_income_bill_detail_${year}".business_id IS '业务唯一id';
COMMENT ON COLUMN "${serviceCode}_prepaid_income_bill_detail_${year}".business_time IS '业务产生的时间戳';
COMMENT ON COLUMN "${serviceCode}_prepaid_income_bill_detail_${year}".service_id IS '服务id';
COMMENT ON COLUMN "${serviceCode}_prepaid_income_bill_detail_${year}".service_code IS '服务编码';
COMMENT ON COLUMN "${serviceCode}_prepaid_income_bill_detail_${year}".customer_id IS '客户id';
COMMENT ON COLUMN "${serviceCode}_prepaid_income_bill_detail_${year}".account_id IS '账户id';
COMMENT ON COLUMN "${serviceCode}_prepaid_income_bill_detail_${year}".product_id IS '产品id';
COMMENT ON COLUMN "${serviceCode}_prepaid_income_bill_detail_${year}".entity_id IS '主体id';
COMMENT ON COLUMN "${serviceCode}_prepaid_income_bill_detail_${year}".contract_id IS '合同id';
COMMENT ON COLUMN "${serviceCode}_prepaid_income_bill_detail_${year}".wallet_id IS '钱包id';
COMMENT ON COLUMN "${serviceCode}_prepaid_income_bill_detail_${year}".plan_id IS '计划id';
COMMENT ON COLUMN "${serviceCode}_prepaid_income_bill_detail_${year}".subscribe_id IS '订阅id';
COMMENT ON COLUMN "${serviceCode}_prepaid_income_bill_detail_${year}".discount_id IS '优惠id';
COMMENT ON COLUMN "${serviceCode}_prepaid_income_bill_detail_${year}".usage_count IS '消耗量';
COMMENT ON COLUMN "${serviceCode}_prepaid_income_bill_detail_${year}".usage_unit IS '消耗量单位';
COMMENT ON COLUMN "${serviceCode}_prepaid_income_bill_detail_${year}".charge_unit_count IS '计费单位数';
COMMENT ON COLUMN "${serviceCode}_prepaid_income_bill_detail_${year}".charge_usage_count IS '计费消耗量(measure*charge_unit_count)';
COMMENT ON COLUMN "${serviceCode}_prepaid_income_bill_detail_${year}".charge_measure IS '计量单位数';
COMMENT ON COLUMN "${serviceCode}_prepaid_income_bill_detail_${year}".charge_measure_unit IS '计费计量单位';
COMMENT ON COLUMN "${serviceCode}_prepaid_income_bill_detail_${year}".charge_measure_ceil IS '计量单位是否向上取整 0-不向上取整, 1-向上取整';

COMMENT ON COLUMN "${serviceCode}_prepaid_income_bill_detail_${year}".payment_method IS '支付方式 0-现金, 1-积分';
COMMENT ON COLUMN "${serviceCode}_prepaid_income_bill_detail_${year}".billing_type IS '计费类型 0-固定费率，1-阶梯费率，2-套餐计费，3-按量计费';
COMMENT ON COLUMN "${serviceCode}_prepaid_income_bill_detail_${year}".in_trial IS '是否在计费类型试用期内';
COMMENT ON COLUMN "${serviceCode}_prepaid_income_bill_detail_${year}".bill_status IS '账单状态 0-草稿, 1-待支付, 2-已支付, 3-未结清';
COMMENT ON COLUMN "${serviceCode}_prepaid_income_bill_detail_${year}".wallet_deduct_status IS '钱包扣款状态（0-未扣款、1-成功、2-失败、3-处理中, 4-退款）';
COMMENT ON COLUMN "${serviceCode}_prepaid_income_bill_detail_${year}".point_amount IS '积分';
COMMENT ON COLUMN "${serviceCode}_prepaid_income_bill_detail_${year}".cash_amount IS '现金';
COMMENT ON COLUMN "${serviceCode}_prepaid_income_bill_detail_${year}".tax_rate IS '税率';
COMMENT ON COLUMN "${serviceCode}_prepaid_income_bill_detail_${year}".original_price IS '目录总价(原单价)';
COMMENT ON COLUMN "${serviceCode}_prepaid_income_bill_detail_${year}".discounted_price IS '订阅总价(优惠的目录价)';
COMMENT ON COLUMN "${serviceCode}_prepaid_income_bill_detail_${year}".discount_amount IS '优惠的金额';
COMMENT ON COLUMN "${serviceCode}_prepaid_income_bill_detail_${year}".invoiced_amount IS '已开票金额';
COMMENT ON COLUMN "${serviceCode}_prepaid_income_bill_detail_${year}".available_invoice_amount IS '可开票金额(=优惠价-已开票金额)';
COMMENT ON COLUMN "${serviceCode}_prepaid_income_bill_detail_${year}".amount_with_tax IS '含税总金额';
COMMENT ON COLUMN "${serviceCode}_prepaid_income_bill_detail_${year}".amount_without_tax IS '不含税金额';
COMMENT ON COLUMN "${serviceCode}_prepaid_income_bill_detail_${year}".paid_amount IS '已支付金额';
COMMENT ON COLUMN "${serviceCode}_prepaid_income_bill_detail_${year}".unpaid_amount IS '未支付金额';
COMMENT ON COLUMN "${serviceCode}_prepaid_income_bill_detail_${year}".currency IS '货币单位 CNY USD';
COMMENT ON COLUMN "${serviceCode}_prepaid_income_bill_detail_${year}".payment_time IS '实际支付时间戳';
COMMENT ON COLUMN "${serviceCode}_prepaid_income_bill_detail_${year}".billing_time IS '出账时间戳';
COMMENT ON COLUMN "${serviceCode}_prepaid_income_bill_detail_${year}".billing_start_time IS '出账开始时间戳（毫秒）';
COMMENT ON COLUMN "${serviceCode}_prepaid_income_bill_detail_${year}".billing_end_time IS '出账结束时间戳（毫秒）';
COMMENT ON COLUMN "${serviceCode}_prepaid_income_bill_detail_${year}".billing_cycle_type IS '间隔时长单位, 0：一次性, 1:日，2：周，3：月，4：季度，5：年';
COMMENT ON COLUMN "${serviceCode}_prepaid_income_bill_detail_${year}".billing_day IS '间隔时长';
COMMENT ON COLUMN "${serviceCode}_prepaid_income_bill_detail_${year}".billing_cycle IS '出账周期标识（如202507-202509）';
COMMENT ON COLUMN "${serviceCode}_prepaid_income_bill_detail_${year}".create_time IS '数据创建时间戳';
COMMENT ON COLUMN "${serviceCode}_prepaid_income_bill_detail_${year}".deleted IS '是否删除';
COMMENT ON COLUMN "${serviceCode}_prepaid_income_bill_detail_${year}".request_id IS '接口请求id';
COMMENT ON COLUMN "${serviceCode}_prepaid_income_bill_detail_${year}".callback_url IS '回调地址，异步通知使用';
COMMENT ON COLUMN "${serviceCode}_prepaid_income_bill_detail_${year}".callback_status IS '回调状态：0-未回调，1-回调成功，2-回调失败';
COMMENT ON COLUMN "${serviceCode}_prepaid_income_bill_detail_${year}".extend_data IS '扩展数据JSON，存储特殊业务数据';
COMMENT ON COLUMN "${serviceCode}_prepaid_income_bill_detail_${year}".rate_details IS '费用详情json 见 com.linkcircle.boss.module.billing.api.rate.model.dto.UsageBasedRateConfigDTO';
COMMENT ON COLUMN "${serviceCode}_prepaid_income_bill_detail_${year}".discount_details IS '服务优惠详情json 见 List<com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.Coupon>';

COMMENT ON COLUMN "${serviceCode}_prepaid_income_bill_detail_${year}".caller_number IS '主叫号码';
COMMENT ON COLUMN "${serviceCode}_prepaid_income_bill_detail_${year}".callee_number IS '被叫号码';

-- 后付费收入账单明细表
drop TABLE "${serviceCode}_postpaid_income_bill_detail_${year}";
CREATE TABLE "${serviceCode}_postpaid_income_bill_detail_${year}"
(
    bill_detail_id           BIGINT         NOT NULL,          -- 收入账单明细id
    business_id              VARCHAR(128)   NOT NULL,          -- 业务唯一id
    business_time            BIGINT         NOT NULL,          -- 业务产生的时间戳
    service_id               BIGINT         NOT NULL,          -- 服务id
    service_code             VARCHAR(128)   NOT NULL,          -- 服务编码
    customer_id              BIGINT         NOT NULL,          -- 客户id
    account_id               BIGINT         NOT NULL,          -- 账户id
    product_id               BIGINT         NOT NULL,          -- 产品id
    entity_id                BIGINT         NOT NULL,          -- 主体id
    contract_id              BIGINT              DEFAULT NULL, -- 合同id
    wallet_id                BIGINT         NOT NULL,          -- 钱包id
    plan_id                  BIGINT              DEFAULT NULL, -- 计划id
    discount_id              VARCHAR(128)        DEFAULT NULL, -- 优惠id
    subscribe_id             BIGINT         NULL,              -- 订阅id
    usage_count              DECIMAL(18, 2) NOT NULL,          -- 消耗量
    usage_unit               VARCHAR(16)    NULL,              -- 消耗量单位
    charge_usage_count       DECIMAL(18, 2) NOT NULL,          -- 计费消耗量(measure*charge_unit_count)
    charge_measure           DECIMAL(18, 2) NOT NULL,          -- 计量单位数
    charge_unit_count        DECIMAL(18, 2) NOT NULL,          -- 计费单位数
    charge_measure_unit      VARCHAR(16)    NULL,              -- 计费计量单位
    charge_measure_ceil      SMALLINT       NOT NULL,          -- 计量单位是否向上取整 0-不向上取整, 1-向上取整
    payment_method           SMALLINT       NOT NULL,          -- 支付方式 0-现金, 1-积分
    billing_type             SMALLINT       NOT NULL,          -- 计费类型 0-固定费率，1-阶梯费率，2-套餐计费，3-按量计费
    in_trial                 BOOLEAN,                          -- 在试用期内
    bill_status              SMALLINT       NOT NULL,          -- 账单状态 0-草稿, 1-待支付, 2-已支付, 3-未结清
    wallet_deduct_status     SMALLINT       NULL DEFAULT 0,    -- 钱包扣款状态（0-未扣款、1-成功、2-失败、3-处理中, 4-退款）
    point_amount             DECIMAL(18, 0) NULL DEFAULT 0,    -- 积分
    cash_amount              DECIMAL(18, 6) NULL DEFAULT 0,    -- 现金
    tax_rate                 DECIMAL(5, 4)       DEFAULT 0,    -- 税率
    original_unit_price      DECIMAL(18, 6) NULL DEFAULT 0,    -- 目录价(原单价)
    discounted_unit_price    DECIMAL(18, 6) NULL DEFAULT 0,    -- 订阅价(优惠的目录价)
    original_price           DECIMAL(18, 6)      DEFAULT 0,    -- 目录总价(原单价)
    discounted_price         DECIMAL(18, 6)      DEFAULT 0,    -- 订阅总价(优惠的目录价)
    discount_amount          DECIMAL(18, 6)      DEFAULT 0,    -- 优惠的金额
    invoiced_amount          DECIMAL(18, 6)      DEFAULT 0,    -- 已开票金额
    available_invoice_amount DECIMAL(18, 6)      DEFAULT 0,    -- 可开票金额(=优惠价-已开票金额)
    amount_with_tax          DECIMAL(18, 6)      DEFAULT 0,    -- 含税总金额
    amount_without_tax       DECIMAL(18, 6)      DEFAULT 0,    -- 不含税金额
    currency                 VARCHAR(3)          DEFAULT NULL, -- 货币单位 CNY USD
    payment_time             BIGINT              DEFAULT NULL, -- 实际支付时间戳
    billing_time             BIGINT              DEFAULT NULL, -- 出账时间戳
    billing_start_time       BIGINT              DEFAULT NULL, -- 出账开始时间戳（毫秒）
    billing_end_time         BIGINT              DEFAULT NULL, -- 出账结束时间戳（毫秒）
    billing_cycle_type       SMALLINT            DEFAULT NULL, -- 间隔时长单位, 0：一次性, 1:日，2：周，3：月，4：季度，5：年
    billing_day              INTEGER             DEFAULT NULL, -- 间隔时长
    billing_cycle            VARCHAR(32)         DEFAULT NULL, -- 出账周期标识（如202507-202509）
    create_time              BIGINT         NOT NULL,          -- 数据创建时间戳
    deleted                  BOOLEAN,                          -- 是否删除
    request_id               VARCHAR(128)   NOT NULL,          -- 接口请求id
    extend_data              JSON                DEFAULT NULL, -- 扩展数据JSON，存储特殊业务数据
    rate_details             JSON                DEFAULT NULL, -- 费用详情json
    discount_details         JSON                DEFAULT NULL, -- 服务优惠详情json

    CONSTRAINT pk_postpaid_income_bill_detail PRIMARY KEY (bill_detail_id, business_id)
);

-- 为后付费收入账单明细表创建索引
CREATE INDEX idx_postpaid_income_account_id ON "${serviceCode}_postpaid_income_bill_detail_${year}" (account_id);
CREATE INDEX idx_postpaid_income_customer_id ON "${serviceCode}_postpaid_income_bill_detail_${year}" (customer_id);
CREATE INDEX idx_postpaid_income_product_id ON "${serviceCode}_postpaid_income_bill_detail_${year}" (product_id);

-- 添加表注释
COMMENT ON TABLE "${serviceCode}_postpaid_income_bill_detail_${year}" IS '后付费-收入账单-明细表 ${serviceCode} - ${year}';

-- 添加字段注释
COMMENT ON COLUMN "${serviceCode}_postpaid_income_bill_detail_${year}".bill_detail_id IS '收入账单明细id';
COMMENT ON COLUMN "${serviceCode}_postpaid_income_bill_detail_${year}".business_id IS '业务唯一id';
COMMENT ON COLUMN "${serviceCode}_postpaid_income_bill_detail_${year}".business_time IS '业务产生的时间戳';
COMMENT ON COLUMN "${serviceCode}_postpaid_income_bill_detail_${year}".service_id IS '服务id';
COMMENT ON COLUMN "${serviceCode}_postpaid_income_bill_detail_${year}".service_code IS '服务编码';
COMMENT ON COLUMN "${serviceCode}_postpaid_income_bill_detail_${year}".customer_id IS '客户id';
COMMENT ON COLUMN "${serviceCode}_postpaid_income_bill_detail_${year}".account_id IS '账户id';
COMMENT ON COLUMN "${serviceCode}_postpaid_income_bill_detail_${year}".product_id IS '产品id';
COMMENT ON COLUMN "${serviceCode}_postpaid_income_bill_detail_${year}".entity_id IS '主体id';
COMMENT ON COLUMN "${serviceCode}_postpaid_income_bill_detail_${year}".contract_id IS '合同id';
COMMENT ON COLUMN "${serviceCode}_postpaid_income_bill_detail_${year}".wallet_id IS '钱包id';
COMMENT ON COLUMN "${serviceCode}_postpaid_income_bill_detail_${year}".plan_id IS '计划id';
COMMENT ON COLUMN "${serviceCode}_postpaid_income_bill_detail_${year}".discount_id IS '优惠id';
COMMENT ON COLUMN "${serviceCode}_postpaid_income_bill_detail_${year}".subscribe_id IS '订阅id';
COMMENT ON COLUMN "${serviceCode}_postpaid_income_bill_detail_${year}".usage_count IS '消耗量';
COMMENT ON COLUMN "${serviceCode}_postpaid_income_bill_detail_${year}".usage_unit IS '消耗量单位';
COMMENT ON COLUMN "${serviceCode}_postpaid_income_bill_detail_${year}".charge_unit_count IS '计费单位数';
COMMENT ON COLUMN "${serviceCode}_postpaid_income_bill_detail_${year}".charge_usage_count IS '计费消耗量(measure*charge_unit_count)';
COMMENT ON COLUMN "${serviceCode}_postpaid_income_bill_detail_${year}".charge_measure IS '计量单位数';
COMMENT ON COLUMN "${serviceCode}_postpaid_income_bill_detail_${year}".charge_measure_unit IS '计费计量单位';
COMMENT ON COLUMN "${serviceCode}_postpaid_income_bill_detail_${year}".charge_measure_ceil IS '计量单位是否向上取整 0-不向上取整, 1-向上取整';

COMMENT ON COLUMN "${serviceCode}_postpaid_income_bill_detail_${year}".payment_method IS '支付方式 0-现金, 1-积分';
COMMENT ON COLUMN "${serviceCode}_postpaid_income_bill_detail_${year}".billing_type IS '计费类型 0-固定费率，1-阶梯费率，2-套餐计费，3-按量计费';
COMMENT ON COLUMN "${serviceCode}_postpaid_income_bill_detail_${year}".in_trial IS '在试用期内';
COMMENT ON COLUMN "${serviceCode}_postpaid_income_bill_detail_${year}".bill_status IS '账单状态 0-草稿, 1-待支付, 2-已支付, 3-未结清';
COMMENT ON COLUMN "${serviceCode}_postpaid_income_bill_detail_${year}".wallet_deduct_status IS '钱包扣款状态（0-未扣款、1-成功、2-失败、3-处理中, 4-退款）';
COMMENT ON COLUMN "${serviceCode}_postpaid_income_bill_detail_${year}".point_amount IS '积分';
COMMENT ON COLUMN "${serviceCode}_postpaid_income_bill_detail_${year}".cash_amount IS '现金';
COMMENT ON COLUMN "${serviceCode}_postpaid_income_bill_detail_${year}".tax_rate IS '税率';
COMMENT ON COLUMN "${serviceCode}_postpaid_income_bill_detail_${year}".original_price IS '目录总价(原单价)';
COMMENT ON COLUMN "${serviceCode}_postpaid_income_bill_detail_${year}".discounted_price IS '订阅总价(优惠的目录价)';
COMMENT ON COLUMN "${serviceCode}_postpaid_income_bill_detail_${year}".discount_amount IS '优惠的金额';
COMMENT ON COLUMN "${serviceCode}_postpaid_income_bill_detail_${year}".invoiced_amount IS '已开票金额';
COMMENT ON COLUMN "${serviceCode}_postpaid_income_bill_detail_${year}".available_invoice_amount IS '可开票金额(=优惠价-已开票金额)';
COMMENT ON COLUMN "${serviceCode}_postpaid_income_bill_detail_${year}".amount_with_tax IS '含税总金额';
COMMENT ON COLUMN "${serviceCode}_postpaid_income_bill_detail_${year}".amount_without_tax IS '不含税金额';
COMMENT ON COLUMN "${serviceCode}_postpaid_income_bill_detail_${year}".currency IS '货币单位 CNY USD';
COMMENT ON COLUMN "${serviceCode}_postpaid_income_bill_detail_${year}".payment_time IS '实际支付时间戳';
COMMENT ON COLUMN "${serviceCode}_postpaid_income_bill_detail_${year}".billing_time IS '出账时间戳';
COMMENT ON COLUMN "${serviceCode}_postpaid_income_bill_detail_${year}".billing_start_time IS '出账开始时间戳（毫秒）';
COMMENT ON COLUMN "${serviceCode}_postpaid_income_bill_detail_${year}".billing_end_time IS '出账结束时间戳（毫秒）';
COMMENT ON COLUMN "${serviceCode}_postpaid_income_bill_detail_${year}".billing_cycle_type IS '间隔时长单位, 0：一次性, 1:日，2：周，3：月，4：季度，5：年';
COMMENT ON COLUMN "${serviceCode}_postpaid_income_bill_detail_${year}".billing_day IS '间隔时长';
COMMENT ON COLUMN "${serviceCode}_postpaid_income_bill_detail_${year}".billing_cycle IS '出账周期标识（如202507-202509）';
COMMENT ON COLUMN "${serviceCode}_postpaid_income_bill_detail_${year}".create_time IS '数据创建时间戳';
COMMENT ON COLUMN "${serviceCode}_postpaid_income_bill_detail_${year}".deleted IS '是否删除';
COMMENT ON COLUMN "${serviceCode}_postpaid_income_bill_detail_${year}".request_id IS '接口请求id';
COMMENT ON COLUMN "${serviceCode}_postpaid_income_bill_detail_${year}".extend_data IS '扩展数据JSON，存储特殊业务数据';
COMMENT ON COLUMN "${serviceCode}_postpaid_income_bill_detail_${year}".rate_details IS '费用详情json 见 com.linkcircle.boss.module.billing.api.rate.model.dto.UsageBasedRateConfigDTO';
COMMENT ON COLUMN "${serviceCode}_postpaid_income_bill_detail_${year}".discount_details IS '服务优惠详情json 见 List<com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.Coupon>';

-- 预付费成本账单明细表
drop TABLE "${serviceCode}_prepaid_cost_bill_detail_${year}";
CREATE TABLE "${serviceCode}_prepaid_cost_bill_detail_${year}"
(
    bill_detail_id      BIGINT         NOT NULL,          -- 成本账单明细id
    business_id         VARCHAR(128)   NOT NULL,          -- 业务唯一id
    business_time       BIGINT         NOT NULL,          -- 业务产生的时间戳
    resource_service_id BIGINT         NOT NULL,          -- 资源服务id
    service_code        VARCHAR(128)   NOT NULL,          -- 服务编码
    supplier_id         BIGINT         NOT NULL,          -- 供应商id
    account_id          BIGINT         NOT NULL,          -- 账户id
    resource_id         BIGINT         NOT NULL,          -- 资源id
    entity_id           BIGINT         NOT NULL,          -- 主体id
    purchase_id         BIGINT         NULL,              -- 采购id
    usage_count         DECIMAL(18, 2) NOT NULL,          -- 消耗量
    usage_unit          VARCHAR(16)    NULL,              -- 消耗量单位
    charge_usage_count  DECIMAL(18, 2) NOT NULL,          -- 计费消耗量(measure*charge_unit_count)
    charge_measure      DECIMAL(18, 2) NOT NULL,          -- 计量单位数
    charge_unit_count   DECIMAL(18, 2) NOT NULL,          -- 计费单位数
    charge_measure_unit VARCHAR(16)    NULL,              -- 计费计量单位
    charge_measure_ceil SMALLINT       NOT NULL,          -- 计量单位是否向上取整 0-不向上取整, 1-向上取整
    original_unit_price DECIMAL(18, 6) NULL DEFAULT 0,    -- 目录价(原单价)
    original_price      DECIMAL(18, 6)      DEFAULT 0,    -- 目录总价(原单价)
    payment_type        SMALLINT       NOT NULL,          -- 支付类型 0-预付费, 1-后付费
    payment_method      SMALLINT       NOT NULL,          -- 支付方式 0-现金, 1-积分
    billing_type        SMALLINT       NOT NULL,          -- 计费类型 0-固定费率，1-阶梯费率，2-套餐计费，3-按量计费
    in_trial            BOOLEAN        NULL,              -- 是否在试用期内
    bill_status         SMALLINT       NOT NULL,          -- 账单状态 0-草稿, 1-待支付, 2-已支付, 3-未结清
    tax_rate            DECIMAL(5, 4)       DEFAULT 0,    -- 税率
    point_amount        DECIMAL(18, 0) NULL DEFAULT 0,    -- 积分
    cash_amount         DECIMAL(18, 6) NULL DEFAULT 0,    -- 现金
    currency            VARCHAR(3)          DEFAULT NULL, -- 货币单位 CNY USD
    payment_time        BIGINT              DEFAULT NULL, -- 实际支付时间戳
    billing_time        BIGINT              DEFAULT NULL, -- 出账时间戳
    create_time         BIGINT              DEFAULT NULL, -- 数据创建时间戳
    deleted             BOOLEAN,                          -- 是否删除
    request_id          VARCHAR(128)   NOT NULL,          -- 接口请求id
    extend_data         JSON                DEFAULT NULL, -- 扩展数据JSON，存储特殊业务数据
    rate_details        JSON                DEFAULT NULL, -- 费用详情json

    CONSTRAINT pk_prepaid_cost_bill_detail PRIMARY KEY (bill_detail_id, business_id)
);

-- 为预付费成本账单明细表创建索引
CREATE INDEX idx_prepaid_cost_account_id ON "${serviceCode}_prepaid_cost_bill_detail_${year}" (account_id);
CREATE INDEX idx_prepaid_cost_resource_id ON "${serviceCode}_prepaid_cost_bill_detail_${year}" (resource_id);
CREATE INDEX idx_prepaid_cost_supplier_id ON "${serviceCode}_prepaid_cost_bill_detail_${year}" (supplier_id);

-- 添加表注释
COMMENT ON TABLE "${serviceCode}_prepaid_cost_bill_detail_${year}" IS '预付费-成本账单-明细表 ${serviceCode} - ${year}';

-- 添加字段注释
COMMENT ON COLUMN "${serviceCode}_prepaid_cost_bill_detail_${year}".bill_detail_id IS '成本账单明细id';
COMMENT ON COLUMN "${serviceCode}_prepaid_cost_bill_detail_${year}".business_id IS '业务唯一id';
COMMENT ON COLUMN "${serviceCode}_prepaid_cost_bill_detail_${year}".business_time IS '业务产生的时间戳';
COMMENT ON COLUMN "${serviceCode}_prepaid_cost_bill_detail_${year}".resource_service_id IS '资源服务id';
COMMENT ON COLUMN "${serviceCode}_prepaid_cost_bill_detail_${year}".service_code IS '服务编码';
COMMENT ON COLUMN "${serviceCode}_prepaid_cost_bill_detail_${year}".supplier_id IS '供应商id';
COMMENT ON COLUMN "${serviceCode}_prepaid_cost_bill_detail_${year}".account_id IS '账户id';
COMMENT ON COLUMN "${serviceCode}_prepaid_cost_bill_detail_${year}".resource_id IS '资源id';
COMMENT ON COLUMN "${serviceCode}_prepaid_cost_bill_detail_${year}".entity_id IS '主体id';
COMMENT ON COLUMN "${serviceCode}_prepaid_cost_bill_detail_${year}".purchase_id IS '采购id';
COMMENT ON COLUMN "${serviceCode}_prepaid_cost_bill_detail_${year}".usage_count IS '消耗量';
COMMENT ON COLUMN "${serviceCode}_prepaid_cost_bill_detail_${year}".usage_unit IS '消耗量单位';
COMMENT ON COLUMN "${serviceCode}_prepaid_cost_bill_detail_${year}".charge_unit_count IS '计费单位数';
COMMENT ON COLUMN "${serviceCode}_prepaid_cost_bill_detail_${year}".charge_usage_count IS '计费消耗量(measure*charge_unit_count)';
COMMENT ON COLUMN "${serviceCode}_prepaid_cost_bill_detail_${year}".charge_measure IS '计量单位数';
COMMENT ON COLUMN "${serviceCode}_prepaid_cost_bill_detail_${year}".charge_measure_unit IS '计费计量单位';
COMMENT ON COLUMN "${serviceCode}_prepaid_cost_bill_detail_${year}".charge_measure_ceil IS '计量单位是否向上取整 0-不向上取整, 1-向上取整';

COMMENT ON COLUMN "${serviceCode}_prepaid_cost_bill_detail_${year}".payment_type IS '支付类型 0-预付费, 1-后付费';
COMMENT ON COLUMN "${serviceCode}_prepaid_cost_bill_detail_${year}".payment_method IS '支付方式 0-现金, 1-积分';
COMMENT ON COLUMN "${serviceCode}_prepaid_cost_bill_detail_${year}".billing_type IS '计费类型 0-固定费率，1-阶梯费率，2-套餐计费，3-按量计费';
COMMENT ON COLUMN "${serviceCode}_prepaid_cost_bill_detail_${year}".in_trial IS '是否在试用期内';
COMMENT ON COLUMN "${serviceCode}_prepaid_cost_bill_detail_${year}".bill_status IS '账单状态 0-草稿, 1-待支付, 2-已支付, 3-未结清';
COMMENT ON COLUMN "${serviceCode}_prepaid_cost_bill_detail_${year}".tax_rate IS '税率';
COMMENT ON COLUMN "${serviceCode}_prepaid_cost_bill_detail_${year}".point_amount IS '积分';
COMMENT ON COLUMN "${serviceCode}_prepaid_cost_bill_detail_${year}".cash_amount IS '现金';
COMMENT ON COLUMN "${serviceCode}_prepaid_cost_bill_detail_${year}".currency IS '货币单位 CNY USD';
COMMENT ON COLUMN "${serviceCode}_prepaid_cost_bill_detail_${year}".payment_time IS '实际支付时间戳';
COMMENT ON COLUMN "${serviceCode}_prepaid_cost_bill_detail_${year}".billing_time IS '出账时间戳';
COMMENT ON COLUMN "${serviceCode}_prepaid_cost_bill_detail_${year}".create_time IS '数据创建时间戳';
COMMENT ON COLUMN "${serviceCode}_prepaid_cost_bill_detail_${year}".deleted IS '是否删除';
COMMENT ON COLUMN "${serviceCode}_prepaid_cost_bill_detail_${year}".request_id IS '接口请求id';
COMMENT ON COLUMN "${serviceCode}_prepaid_cost_bill_detail_${year}".extend_data IS '扩展数据JSON，存储特殊业务数据';
COMMENT ON COLUMN "${serviceCode}_prepaid_cost_bill_detail_${year}".rate_details IS '费用详情json 见 com.linkcircle.boss.module.billing.api.rate.model.dto.UsageBasedRateConfigDTO';

-- 后付费成本账单明细表
drop TABLE "${serviceCode}_postpaid_cost_bill_detail_${year}";
CREATE TABLE "${serviceCode}_postpaid_cost_bill_detail_${year}"
(
    bill_detail_id      BIGINT         NOT NULL,          -- 成本账单明细id
    business_id         VARCHAR(128)   NOT NULL,          -- 业务唯一id
    business_time       BIGINT         NOT NULL,          -- 业务产生的时间戳
    resource_service_id BIGINT         NOT NULL,          -- 资源服务id
    service_code        VARCHAR(128)   NOT NULL,          -- 服务编码
    supplier_id         BIGINT         NOT NULL,          -- 供应商id
    account_id          BIGINT         NOT NULL,          -- 账户id
    resource_id         BIGINT         NOT NULL,          -- 资源id
    entity_id           BIGINT         NOT NULL,          -- 主体id
    purchase_id         BIGINT         NULL,              -- 采购id
    usage_count         DECIMAL(18, 2) NOT NULL,          -- 消耗量
    usage_unit          VARCHAR(16)    NULL,              -- 消耗量单位
    charge_unit_count   DECIMAL(18, 2) NOT NULL,          -- 计费单位数
    charge_usage_count  DECIMAL(18, 2) NOT NULL,          -- 计费消耗量(measure*charge_unit_count)
    charge_measure      DECIMAL(18, 2) NOT NULL,          -- 计量单位数
    charge_measure_unit VARCHAR(16)    NULL,              -- 计费计量单位
    charge_measure_ceil SMALLINT       NOT NULL,          -- 计量单位是否向上取整 0-不向上取整, 1-向上取整
    original_unit_price DECIMAL(18, 6) NULL DEFAULT 0,    -- 目录价(原单价)
    original_price      DECIMAL(18, 6)      DEFAULT 0,    -- 目录总价(原单价)
    payment_type        SMALLINT       NOT NULL,          -- 支付类型 0-预付费, 1-后付费
    payment_method      SMALLINT       NOT NULL,          -- 支付方式 0-现金, 1-积分
    billing_type        SMALLINT       NOT NULL,          -- 计费类型 0-固定费率，1-阶梯费率，2-套餐计费，3-按量计费
    in_trial            BOOLEAN        NULL,              -- 在试用期内
    bill_status         SMALLINT       NOT NULL,          -- 账单状态 0-草稿, 1-待支付, 2-已支付, 3-未结清
    tax_rate            DECIMAL(5, 4)       DEFAULT 0,    -- 税率
    point_amount        DECIMAL(18, 0) NULL DEFAULT 0,    -- 积分
    cash_amount         DECIMAL(18, 6) NULL DEFAULT 0,    -- 现金
    currency            VARCHAR(3)          DEFAULT NULL, -- 货币单位 CNY USD
    payment_time        BIGINT              DEFAULT NULL, -- 实际支付时间戳
    billing_time        BIGINT              DEFAULT NULL, -- 出账时间戳
    create_time         BIGINT         NOT NULL,          -- 数据创建时间戳
    deleted             BOOLEAN        NULL,              -- 是否删除
    request_id          VARCHAR(128)   NOT NULL,          -- 接口请求id
    extend_data         JSON                DEFAULT NULL, -- 扩展数据JSON，存储特殊业务数据
    rate_details        JSON                DEFAULT NULL, -- 费用详情json

    CONSTRAINT pk_postpaid_cost_bill_detail PRIMARY KEY (bill_detail_id, business_id)
);

-- 为后付费成本账单明细表创建索引
CREATE INDEX idx_postpaid_cost_account_id ON "${serviceCode}_postpaid_cost_bill_detail_${year}" (account_id);
CREATE INDEX idx_postpaid_cost_resource_id ON "${serviceCode}_postpaid_cost_bill_detail_${year}" (resource_id);
CREATE INDEX idx_postpaid_cost_supplier_id ON "${serviceCode}_postpaid_cost_bill_detail_${year}" (supplier_id);

-- 添加表注释
COMMENT ON TABLE "${serviceCode}_postpaid_cost_bill_detail_${year}" IS '后付费-成本账单-明细表 ${serviceCode} - ${year}';

-- 添加字段注释
COMMENT ON COLUMN "${serviceCode}_postpaid_cost_bill_detail_${year}".bill_detail_id IS '成本账单明细id';
COMMENT ON COLUMN "${serviceCode}_postpaid_cost_bill_detail_${year}".business_id IS '业务唯一id';
COMMENT ON COLUMN "${serviceCode}_postpaid_cost_bill_detail_${year}".business_time IS '业务产生的时间戳';
COMMENT ON COLUMN "${serviceCode}_postpaid_cost_bill_detail_${year}".resource_service_id IS '资源服务id';
COMMENT ON COLUMN "${serviceCode}_postpaid_cost_bill_detail_${year}".service_code IS '服务编码';
COMMENT ON COLUMN "${serviceCode}_postpaid_cost_bill_detail_${year}".supplier_id IS '供应商id';
COMMENT ON COLUMN "${serviceCode}_postpaid_cost_bill_detail_${year}".account_id IS '账户id';
COMMENT ON COLUMN "${serviceCode}_postpaid_cost_bill_detail_${year}".resource_id IS '资源id';
COMMENT ON COLUMN "${serviceCode}_postpaid_cost_bill_detail_${year}".entity_id IS '主体id';
COMMENT ON COLUMN "${serviceCode}_postpaid_cost_bill_detail_${year}".purchase_id IS '采购id';
COMMENT ON COLUMN "${serviceCode}_postpaid_cost_bill_detail_${year}".usage_count IS '消耗量';
COMMENT ON COLUMN "${serviceCode}_postpaid_cost_bill_detail_${year}".usage_unit IS '消耗量单位';
COMMENT ON COLUMN "${serviceCode}_postpaid_cost_bill_detail_${year}".charge_unit_count IS '计费单位数';
COMMENT ON COLUMN "${serviceCode}_postpaid_cost_bill_detail_${year}".charge_usage_count IS '计费消耗量(measure*charge_unit_count)';
COMMENT ON COLUMN "${serviceCode}_postpaid_cost_bill_detail_${year}".charge_measure IS '计量单位数';
COMMENT ON COLUMN "${serviceCode}_postpaid_cost_bill_detail_${year}".charge_measure_unit IS '计费计量单位';
COMMENT ON COLUMN "${serviceCode}_postpaid_cost_bill_detail_${year}".charge_measure_ceil IS '计量单位是否向上取整 0-不向上取整, 1-向上取整';

COMMENT ON COLUMN "${serviceCode}_postpaid_cost_bill_detail_${year}".payment_type IS '支付类型 0-预付费, 1-后付费';
COMMENT ON COLUMN "${serviceCode}_postpaid_cost_bill_detail_${year}".payment_method IS '支付方式 0-现金, 1-积分';
COMMENT ON COLUMN "${serviceCode}_postpaid_cost_bill_detail_${year}".billing_type IS '计费类型 0-固定费率，1-阶梯费率，2-套餐计费，3-按量计费';
COMMENT ON COLUMN "${serviceCode}_postpaid_cost_bill_detail_${year}".in_trial IS '在试用期内';
COMMENT ON COLUMN "${serviceCode}_postpaid_cost_bill_detail_${year}".bill_status IS '账单状态 0-草稿, 1-待支付, 2-已支付, 3-未结清';
COMMENT ON COLUMN "${serviceCode}_postpaid_cost_bill_detail_${year}".tax_rate IS '税率';
COMMENT ON COLUMN "${serviceCode}_postpaid_cost_bill_detail_${year}".point_amount IS '积分';
COMMENT ON COLUMN "${serviceCode}_postpaid_cost_bill_detail_${year}".cash_amount IS '现金';
COMMENT ON COLUMN "${serviceCode}_postpaid_cost_bill_detail_${year}".currency IS '货币单位 CNY USD';
COMMENT ON COLUMN "${serviceCode}_postpaid_cost_bill_detail_${year}".payment_time IS '实际支付时间戳';
COMMENT ON COLUMN "${serviceCode}_postpaid_cost_bill_detail_${year}".billing_time IS '出账时间戳';
COMMENT ON COLUMN "${serviceCode}_postpaid_cost_bill_detail_${year}".create_time IS '数据创建时间戳';
COMMENT ON COLUMN "${serviceCode}_postpaid_cost_bill_detail_${year}".deleted IS '是否删除';
COMMENT ON COLUMN "${serviceCode}_postpaid_cost_bill_detail_${year}".request_id IS '接口请求id';
COMMENT ON COLUMN "${serviceCode}_postpaid_cost_bill_detail_${year}".extend_data IS '扩展数据JSON，存储特殊业务数据';
COMMENT ON COLUMN "${serviceCode}_postpaid_cost_bill_detail_${year}".rate_details IS '费用详情json 见 com.linkcircle.boss.module.billing.api.rate.model.dto.UsageBasedRateConfigDTO';
