DROP TABLE IF EXISTS `makeup_income_bill_${year}`;
create table `makeup_income_bill_${year}`
(
    bill_id                  bigint comment '账单id',
    customer_id              bigint comment '客户id ',
    account_id               bigint comment '账户id ',
    entity_id                bigint comment '主体id ',
    contract_id              bigint comment '合同id',
    wallet_id                bigint comment '钱包id ',
    bill_status              bigint comment '账单状态 0-草稿 1-待支付 2-已支付 3-未结清 ',
    tax_rate                 decimal(8, 4) comment '税率',
    tax_amount               decimal(18, 6) comment '税率金额总计',
    sub_total_amount         decimal(18, 6) comment '总价各个服务总价之和',
    discount_amount          decimal(18, 6) comment '账单优惠的金额',
    invoiced_amount          decimal(18, 6) comment '已开票金额',
    available_invoice_amount decimal(18, 6) comment '可开票金额(优惠价-已开票金额',
    amount_with_tax          decimal(18, 6) comment '含税总金额->发票金额[可开发票金额的最大值]',
    amount_without_tax       decimal(18, 6) comment '不含税金额',
    currency                 varchar(3) comment '货币单位 CNY USD',
    payment_time             bigint comment '实际支付时间戳',
    billing_time             bigint comment '出账时间戳',
    billing_start_time       bigint comment '账期-开始时间时间戳',
    billing_end_time         bigint comment '账期-结束时间时间戳',
    create_time              bigint comment '数据创建时间戳 ',
    deleted                  tinyint(1) comment '是否删除',
    discount_details         JSON comment '服务优惠详情json 见 List<com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.Coupon>',
    original_price           decimal(18, 6) comment '目录价(原价',
    "paid_amount"            numeric(18, 6) DEFAULT 0 comment '已支付金额',
    "unpaid_amount"          numeric(18, 6) DEFAULT 0 comment '未支付金额',
    bill_code     varchar(50) comment '账单号码-唯一',
    refund_invoice_amount      decimal(18, 6) comment '开票退款金额',
    `wallet_deduct_status`     tinyint        NULL DEFAULT 0 COMMENT '钱包扣款状态（0-未扣款、1-成功、2-失败、3-余额不足, 4-未结清）',
    INDEX idx_customer_id (`customer_id`) USING INVERTED COMMENT '客户ID的Bitmap索引',
    INDEX idx_account_id (`account_id`) USING INVERTED COMMENT '账户ID的Bitmap索引',
    INDEX idx_entity_id (`entity_id`) USING INVERTED COMMENT '主体ID的Bitmap索引',
    INDEX idx_contract_id (`contract_id`) USING INVERTED COMMENT '合同ID的Bitmap索引',
    INDEX idx_billing_time (`billing_time`) USING INVERTED COMMENT '账单时间的Bitmap索引',
    INDEX idx_bill_status (`bill_status`) USING INVERTED COMMENT '状态的Bitmap索引',
    INDEX idx_bill_code (`bill_code`) USING INVERTED COMMENT '账单号索引'
) ENGINE = OLAP UNIQUE KEY(`bill_id`)
comment '手工账单-收入-账单表'
DISTRIBUTED BY HASH(`bill_id`) BUCKETS 10
PROPERTIES (
"replication_allocation" = "tag.location.default: 3",
"min_load_replica_num" = "-1",
"is_being_synced" = "false",
"storage_medium" = "hdd",
"storage_format" = "V2",
"inverted_index_storage_format" = "V2",
"enable_unique_key_merge_on_write" = "true",
"light_schema_change" = "true",
"disable_auto_compaction" = "false",
"enable_single_replica_compaction" = "false",
"group_commit_interval_ms" = "10000",
"group_commit_data_bytes" = "*********",
"enable_mow_light_delete" = "false"
);

DROP TABLE IF EXISTS `makeup_product_income_bill_${year}`;
create table `makeup_product_income_bill_${year}`
(


    product_bill_id    bigint comment '产品账单id',
    bill_id            bigint comment '账单ID ',
    customer_id        bigint comment '客户id ',
    account_id         bigint comment '账户id ',
    entity_id          bigint comment '主体id ',
    contract_id        bigint comment '合同id',
    wallet_id          bigint comment '钱包id ',
    product_id         bigint comment '产品id ',
    billing_time       bigint comment '出账时间戳',
    create_time        bigint comment '数据创建时间戳 ',
    deleted            tinyint(1) comment '是否删除',
    discount_details   JSON comment '服务优惠详情json 见 List<com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.Coupon>',
    original_price     decimal(18, 6) comment '目录价(原价',
    discount_amount    decimal(18, 6) comment '优惠的金额',
    amount_with_tax    decimal(18, 6) comment '含税总金额',
    amount_without_tax decimal(18, 6) comment '不含税金额',
    currency           varchar(3) comment '货币单位 CNY USD',
    tax_amount         decimal(18, 6) comment '税额',
    tax_rate           decimal(8, 4) comment '含税',
    INDEX idx_customer_id (`customer_id`) USING INVERTED COMMENT '客户ID的Bitmap索引',
    INDEX idx_account_id (`account_id`) USING INVERTED COMMENT '账户ID的Bitmap索引',
    INDEX idx_billing_time (`billing_time`) USING INVERTED COMMENT '账单时间的Bitmap索引',
    INDEX idx_product_id (`product_id`) USING INVERTED COMMENT '产品ID的Bitmap索引',
    INDEX idx_bill_id (`bill_id`) USING INVERTED COMMENT '账单ID的Bitmap索引'
) ENGINE = OLAP UNIQUE KEY(`product_bill_id`)
comment '手工账单-收入-产品账单表'
DISTRIBUTED BY HASH(`product_bill_id`) BUCKETS 10
PROPERTIES (
"replication_allocation" = "tag.location.default: 3",
"min_load_replica_num" = "-1",
"is_being_synced" = "false",
"storage_medium" = "hdd",
"storage_format" = "V2",
"inverted_index_storage_format" = "V2",
"enable_unique_key_merge_on_write" = "true",
"light_schema_change" = "true",
"disable_auto_compaction" = "false",
"enable_single_replica_compaction" = "false",
"group_commit_interval_ms" = "10000",
"group_commit_data_bytes" = "*********",
"enable_mow_light_delete" = "false"
);


DROP TABLE IF EXISTS `makeup_product_service_income_bill_${year}`;
create table `makeup_product_service_income_bill_${year}`
(
    product_service_bill_id bigint comment '产品服务账单id',
    product_bill_id         bigint comment '产品账单id ',
    bill_id                 bigint comment '账单id ',
    entity_id               bigint comment '主体id ',
    contract_id             bigint comment '合同id',
    customer_id             bigint comment '客户id ',
    account_id              bigint comment '账户id ',
    wallet_id               bigint comment '钱包id ',
    product_id              bigint comment '产品id ',
    service_id              bigint comment '服务id ',
    usage_count             decimal(18, 6) comment '消耗量',
    usage_unit              varchar(16) comment '消耗量单位',
    tax_rate                decimal(5, 4) comment '税率',
    original_price          decimal(18, 6) comment '目录价(原价',
    discount_amount         decimal(18, 6) comment '优惠的金额',
    amount_with_tax         decimal(18, 6) comment '含税总金额',
    amount_without_tax      decimal(18, 6) comment '不含税金额',
    currency                varchar(3) comment '货币单位 CNY USD',
    billing_time            bigint comment '出账时间戳',
    create_time             bigint comment '数据创建时间戳 ',
    deleted                 bigint comment '是否删除',
    rate_details            JSON comment '费用详情json 见 billing-api com.linkcircle.boss.module.billing.api.rate.model.dto.*',
    discount_details        JSON comment '服务优惠详情json 见 List<com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.Coupon>',
    billing_type            tinyint(1) comment '计费类型  0-固定费率 1-阶梯费率 2-套餐计费 3-按量计费 ',
    INDEX idx_customer_id (`customer_id`) USING INVERTED COMMENT '客户ID的Bitmap索引',
    INDEX idx_account_id (`account_id`) USING INVERTED COMMENT '账户ID的Bitmap索引',
    INDEX idx_billing_time (`billing_time`) USING INVERTED COMMENT '账单时间的Bitmap索引',
    INDEX idx_product_id (`product_id`) USING INVERTED COMMENT '产品ID的Bitmap索引',
    INDEX idx_service_id (`service_id`) USING INVERTED COMMENT '服务ID的Bitmap索引',
    INDEX idx_bill_id (`bill_id`) USING INVERTED COMMENT '账单ID的Bitmap索引',
    INDEX idx_product_bill_id (`product_bill_id`) USING INVERTED COMMENT '产品账单ID的Bitmap索引'
) ENGINE = OLAP UNIQUE KEY(`product_service_bill_id`)
comment '手工账单-收入-产品服务账单表'
DISTRIBUTED BY HASH(`product_service_bill_id`) BUCKETS 10
PROPERTIES (
"replication_allocation" = "tag.location.default: 3",
"min_load_replica_num" = "-1",
"is_being_synced" = "false",
"storage_medium" = "hdd",
"storage_format" = "V2",
"inverted_index_storage_format" = "V2",
"enable_unique_key_merge_on_write" = "true",
"light_schema_change" = "true",
"disable_auto_compaction" = "false",
"enable_single_replica_compaction" = "false",
"group_commit_interval_ms" = "10000",
"group_commit_data_bytes" = "*********",
"enable_mow_light_delete" = "false"
);


