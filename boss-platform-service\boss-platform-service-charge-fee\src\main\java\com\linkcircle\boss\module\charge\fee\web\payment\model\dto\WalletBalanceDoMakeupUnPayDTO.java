package com.linkcircle.boss.module.charge.fee.web.payment.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class WalletBalanceDoMakeupUnPayDTO {

    /**
     * 支付钱包id
     */
    @Schema(description = "钱包ID")
    private Long walletId;


    /**
     * 再次发起支付的 手工账单集合
     */
    @Schema(description = "再次发起支付的手工账单集合")
    @NotNull(message = "再次发起支付的账单集合不能为空")
    @NotEmpty(message = "再次发起支付的账单集合不能为空")
    private List<Long> makeupIncomeBillIds;

}
