package com.linkcircle.boss.module.system.web.user.model.dto.user;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserUpdateDataScopeReqDTO {

    @NotNull(message = "用户ID不能为空")
    private Long id;

    @NotNull
    @Schema(description = "数据范围类型（1：全部数据权限 2：自定数据权限）API接口调用不用传 字典:user_data_scope", example = "1")
    private Integer dataScope;

    @Schema(description = "指定客户ID列表,全选:传-1,其他置灰表示所有客户")
    private Set<Long> dataScopeCustomerIds;

    @Schema(description = "指定供应商ID列表,全选:传-1,其他置灰表示所有供应商")
    private Set<Long> dataScopeSupplierIds;

    @Schema(description = "指定合同ID列表,全选:全选:传-1,其他置灰表示所有合同")
    private Set<Long> dataScopeContractIds;

    @Schema(description = "指定项目ID列表,全选:全选:传-1,其他置灰表示所有项目")
    private Set<Long> dataScopeProjectIds;
}
