package com.linkcircle.boss.billing.sdk;

import com.linkcircle.boss.billing.sdk.builder.CostBillDetailRequestBuilder;
import com.linkcircle.boss.billing.sdk.builder.IncomeBillDetailRequestBuilder;
import com.linkcircle.boss.billing.sdk.config.BillingConfig;

/**
 * <AUTHOR>
 * @date 2025-07-25 15:00
 * @description 计费SDK工厂类
 */
public class BillingSDK {

    /**
     * 创建计费客户端
     *
     * @param baseUrl   计费平台基础URL
     * @param appId     应用ID
     * @param appSecret 应用密钥
     * @param tenantId  租户ID
     * @return 计费客户端
     */
    public static BillingClient createClient(String baseUrl, String appId, String appSecret, Long tenantId) {
        BillingConfig config = BillingConfig.builder()
                .baseUrl(baseUrl)
                .appId(appId)
                .appSecret(appSecret)
                .tenantId(tenantId)
                .build();
        return new BillingClient(config);
    }

    /**
     * 创建计费客户端（带配置）
     *
     * @param config 配置信息
     * @return 计费客户端
     */
    public static BillingClient createClient(BillingConfig config) {
        return new BillingClient(config);
    }

    /**
     * 创建收入账单明细请求构建器
     *
     * @return 构建器
     */
    public static IncomeBillDetailRequestBuilder incomeRequest() {
        return new IncomeBillDetailRequestBuilder();
    }

    /**
     * 创建成本账单明细请求构建器
     *
     * @return 构建器
     */
    public static CostBillDetailRequestBuilder costRequest() {
        return new CostBillDetailRequestBuilder();
    }
}
