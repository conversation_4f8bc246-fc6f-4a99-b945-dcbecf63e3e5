package com.linkcircle.boss.module.billing.web.detail.income.consumer;

import com.linkcircle.boss.framework.common.constants.ChargeTopicConstant;
import com.linkcircle.boss.framework.common.exception.IdempotentException;
import com.linkcircle.boss.framework.common.util.json.JsonUtils;
import com.linkcircle.boss.framework.tanant.TenantContextHolder;
import com.linkcircle.boss.framework.web.context.EnableLoginContext;
import com.linkcircle.boss.module.billing.constants.ResponsibilityChainGroupConstant;
import com.linkcircle.boss.module.billing.exception.RepeatedBillingException;
import com.linkcircle.boss.module.billing.exception.ServiceConfigNotFoundException;
import com.linkcircle.boss.module.billing.web.detail.income.model.dto.ReceiveIncomeBillMqDTO;
import com.linkcircle.boss.module.billing.web.detail.income.service.calculation.chain.context.IncomeDetailBillErrorEnum;
import com.linkcircle.boss.module.billing.web.detail.income.service.calculation.chain.context.IncomeDetailBillRequestContext;
import com.linkcircle.boss.module.billing.web.idempotent.service.IdempotentService;
import com.linkcircle.boss.module.crm.enums.BillTypeEnum;
import io.github.kk01001.design.pattern.responsibility.ResponsibilityChainContext;
import io.github.kk01001.design.pattern.responsibility.ResponsibilityChainFactory;
import io.github.kk01001.design.pattern.strategy.exception.StrategyException;
import io.github.kk01001.util.TraceIdUtil;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.redisson.api.RLock;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import java.time.Duration;

/**
 * <AUTHOR>
 * @date 2025-06-18 14:04
 * @description 预付费收入账单消费者
 */
@Component
@Slf4j
@RequiredArgsConstructor
@RocketMQMessageListener(
        topic = ChargeTopicConstant.CHARGE_NORMAL_TOPIC,
        selectorExpression = ChargeTopicConstant.TAG_RECEIVE_INCOME_BILL,
        consumerGroup = ChargeTopicConstant.GROUP_RECEIVE_INCOME_BILL,
        maxReconsumeTimes = 10
)
public class ReceiveIncomeDetailBillConsumer implements RocketMQListener<ReceiveIncomeBillMqDTO> {

    private final ResponsibilityChainFactory responsibilityChainFactory;
    private final IdempotentService idempotentService;
    private final TransactionTemplate transactionTemplate;

    @SneakyThrows
    @Override
    public void onMessage(ReceiveIncomeBillMqDTO message) {
        handler(message);
    }

    public IncomeDetailBillErrorEnum handler(ReceiveIncomeBillMqDTO message) {
        try {
            String businessId = message.getRequestParams().getBusinessId();
            String billId = message.getBillId();
            TraceIdUtil.buildAndSetTraceId(" ", billId, businessId);
            log.info("接收到收入账单MQ消息, message: {}", JsonUtils.toJsonString(message));
            EnableLoginContext.setContext(false);
            TenantContextHolder.setTenantId(message.getTenantId());

            if (!idempotentService.tryLockProcess(BillTypeEnum.INCOME.name(), billId, Duration.ofSeconds(10))) {
                throw new IdempotentException("收入billId重复消费", Long.valueOf(billId));
            }

            // 添加账户锁
            Long accountId = message.getRequestParams().getAccountId();
            RLock lock = idempotentService.lockAccountProcess(accountId);
            try {
                return transactionTemplate.execute(transactionStatus -> {
                    boolean marked = idempotentService.markIncomeBillProcessed(billId);
                    if (!marked) {
                        log.warn("db billId重复消费, billId: {}", billId);
                        return IncomeDetailBillErrorEnum.DUPLICATE_BILL_CONSUMING;
                    }

                    try {
                        // 处理收入账单
                        ResponsibilityChainContext<IncomeDetailBillRequestContext, IncomeDetailBillErrorEnum> context = buildRequestContext(message);
                        IncomeDetailBillErrorEnum result = responsibilityChainFactory.execute(ResponsibilityChainGroupConstant.INCOME_DETAIL_BILL, context);
                        log.info("收入账单处理完成, billId: {}, result: {}", billId, result);
                        return result;
                    } catch (StrategyException e) {
                        String strategyType = e.getStrategyType();
                        log.error("处理收入账单策略异常, message: {}, strategyType: {}, e: ", JsonUtils.toJsonString(message), strategyType, e.getCause());
                        throw (RuntimeException) e.getCause();
                    } catch (Exception e) {
                        throw (RuntimeException) e;
                    }
                });
            } finally {
                idempotentService.unlockAccountProcess(accountId, lock);
            }

        } catch (Exception e) {
            // 判断异常 有些异常就不重试了 如NPE 数据问题
            log.error("处理收入账单消费异常, message: {}, e: ", JsonUtils.toJsonString(message), e);
            if (!skipException(e)) {
                throw e;
            }
        } finally {
            EnableLoginContext.clearContext();
            TenantContextHolder.clear();
            TraceIdUtil.remove();
        }
        return IncomeDetailBillErrorEnum.SKIP;
    }

    private boolean skipException(Exception e) {
        return e instanceof ServiceConfigNotFoundException
                || e instanceof IdempotentException
                || e instanceof RepeatedBillingException;
    }

    private ResponsibilityChainContext<IncomeDetailBillRequestContext, IncomeDetailBillErrorEnum> buildRequestContext(ReceiveIncomeBillMqDTO message) {
        ResponsibilityChainContext<IncomeDetailBillRequestContext, IncomeDetailBillErrorEnum> context = new ResponsibilityChainContext<>();
        IncomeDetailBillRequestContext requestContext = new IncomeDetailBillRequestContext();
        requestContext.setIncomeBillMqDTO(message);
        requestContext.setBillId(message.getBillId());
        context.setData(requestContext);
        return context;
    }
}
