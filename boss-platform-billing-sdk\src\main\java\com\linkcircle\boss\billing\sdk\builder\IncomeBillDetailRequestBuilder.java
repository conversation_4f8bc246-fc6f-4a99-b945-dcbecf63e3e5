package com.linkcircle.boss.billing.sdk.builder;

import com.linkcircle.boss.billing.sdk.model.request.IncomeBillDetailRequest;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025-07-25 15:00
 * @description 收入账单明细请求构建器
 */
public class IncomeBillDetailRequestBuilder {

    private final IncomeBillDetailRequest request;

    public IncomeBillDetailRequestBuilder() {
        this.request = new IncomeBillDetailRequest();
        this.request.setData(new HashMap<>());
    }

    /**
     * 设置账户ID
     */
    public IncomeBillDetailRequestBuilder accountId(Long accountId) {
        request.setAccountId(accountId);
        return this;
    }

    /**
     * 设置产品ID
     */
    public IncomeBillDetailRequestBuilder productId(Long productId) {
        request.setProductId(productId);
        return this;
    }

    /**
     * 设置服务ID
     */
    public IncomeBillDetailRequestBuilder serviceId(Long serviceId) {
        request.setServiceId(serviceId);
        return this;
    }

    /**
     * 设置业务时间
     */
    public IncomeBillDetailRequestBuilder businessTime(Long businessTime) {
        request.setBusinessTime(businessTime);
        return this;
    }

    /**
     * 设置消耗量
     */
    public IncomeBillDetailRequestBuilder usage(BigDecimal usage) {
        request.setUsage(usage);
        return this;
    }

    /**
     * 设置消耗量（数值）
     */
    public IncomeBillDetailRequestBuilder usage(double usage) {
        request.setUsage(BigDecimal.valueOf(usage));
        return this;
    }

    /**
     * 设置消耗量单位
     */
    public IncomeBillDetailRequestBuilder usageUnit(String usageUnit) {
        request.setUsageUnit(usageUnit);
        return this;
    }

    /**
     * 设置回调URL
     */
    public IncomeBillDetailRequestBuilder callbackUrl(String callbackUrl) {
        request.setCallbackUrl(callbackUrl);
        return this;
    }

    /**
     * 设置业务ID
     */
    public IncomeBillDetailRequestBuilder businessId(String businessId) {
        request.setBusinessId(businessId);
        return this;
    }

    /**
     * 设置请求ID
     */
    public IncomeBillDetailRequestBuilder requestId(String requestId) {
        request.setRequestId(requestId);
        return this;
    }

    /**
     * 添加业务数据
     */
    public IncomeBillDetailRequestBuilder addData(String key, Object value) {
        request.getData().put(key, value);
        return this;
    }

    /**
     * 设置业务数据
     */
    public IncomeBillDetailRequestBuilder data(Map<String, Object> data) {
        request.setData(data);
        return this;
    }

    /**
     * 构建请求对象
     */
    public IncomeBillDetailRequest build() {
        return request;
    }
}
