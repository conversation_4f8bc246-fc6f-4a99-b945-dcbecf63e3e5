package com.linkcircle.boss.module.charge.crm.web.plan.model.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "创建/编辑计划请求")
public class ChargePlanCreateReqDTO {

    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "计划ID，编辑时必填")
    private Long id;

    @NotBlank(message = "计划名称不能为空")
    @Schema(description = "计划名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String planName;

    @NotEmpty(message = "产品不能为空")
    @Schema(description = "产品", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<ChargePlanProductCreateReqDTO> products;

    @Schema(description = "描述")
    private String remark;

    @NotNull(message = "支付方式不能为空")
    @Schema(description = "支付方式 0-现金 1-积分 字典:plan_payment_options")
    private Integer paymentOptions;

    @Schema(description = "货币代码", example = "CNY")
    private String currencyCode;

    @NotNull(message = "支付类型不能为空")
    @Schema(description = "支付类型 0-预付费 1-后付费 字典:plan_payment_type")
    private Integer paymentType;

    @Schema(description = "状态 0-未激活 1-激活 2-存档 字典:plan_status")
    private Integer status;

    @Schema(description = "产品Json")
    private String productJson;

}