package com.linkcircle.boss.billing.sdk.util;

import cn.hutool.crypto.digest.DigestUtil;

import java.util.Map;
import java.util.TreeMap;

/**
 * <AUTHOR>
 * @date 2025-07-25 15:00
 * @description 签名工具类
 */
public class SignUtil {

    /**
     * 生成签名
     *
     * @param params    参数Map
     * @param secretKey 密钥
     * @return 签名字符串
     */
    public static String generateSign(Map<String, Object> params, String secretKey) {
        // 1. 移除sign字段
        Map<String, Object> signParams = new TreeMap<>(params);
        signParams.remove("sign");

        // 2. 按key排序并拼接参数
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, Object> entry : signParams.entrySet()) {
            if (entry.getValue() != null) {
                if (sb.length() > 0) {
                    sb.append("&");
                }
                sb.append(entry.getKey()).append("=").append(entry.getValue());
            }
        }

        // 3. 添加密钥
        sb.append("&secret_key=").append(secretKey);

        // 4. SHA256加密
        return DigestUtil.sha256Hex(sb.toString());
    }

    /**
     * 验证签名
     *
     * @param params    参数Map
     * @param secretKey 密钥
     * @param sign      待验证的签名
     * @return 是否验证通过
     */
    public static boolean verifySign(Map<String, Object> params, String secretKey, String sign) {
        String calculatedSign = generateSign(params, secretKey);
        return calculatedSign.equals(sign);
    }
}
