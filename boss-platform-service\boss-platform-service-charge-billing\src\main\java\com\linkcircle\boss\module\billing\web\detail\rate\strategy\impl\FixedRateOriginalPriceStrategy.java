package com.linkcircle.boss.module.billing.web.detail.rate.strategy.impl;

import com.linkcircle.boss.module.billing.api.rate.model.dto.FixedRateConfigDTO;
import com.linkcircle.boss.module.billing.constants.RateTypeConstant;
import com.linkcircle.boss.module.billing.enums.OriginalPriceRateTypeEnum;
import com.linkcircle.boss.module.billing.web.detail.rate.strategy.context.OriginalPriceCalculateRequest;
import com.linkcircle.boss.module.billing.web.detail.rate.strategy.context.OriginalPriceCalculateResponse;
import io.github.kk01001.design.pattern.strategy.IStrategy;
import io.github.kk01001.design.pattern.strategy.annotation.Strategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-06-24 13:43
 * @description 固定费率原价计算策略
 */
@Slf4j
@Component
@RequiredArgsConstructor
@Strategy(strategyEnum = OriginalPriceRateTypeEnum.class, strategyType = RateTypeConstant.FIXED)
public class FixedRateOriginalPriceStrategy extends AbstractOriginalPriceStrategy implements IStrategy<OriginalPriceCalculateRequest, OriginalPriceCalculateResponse> {

    @Override
    public OriginalPriceCalculateResponse execute(OriginalPriceCalculateRequest request) {
        FixedRateConfigDTO rateConfig = (FixedRateConfigDTO) request.getRateConfig();
        Integer paymentOptions = request.getPaymentOptions();
        BigDecimal currentUsage = request.getCurrentUsage();

        // 目录价
        BigDecimal originalUnitPrice = getUnitPrice(paymentOptions, rateConfig.getFixCharge(), rateConfig.getIntegralCharge());

        OriginalPriceCalculateResponse response = OriginalPriceCalculateResponse.success(originalUnitPrice);
        response.setMeasure(BigDecimal.ONE);
        response.setMeasureCeil(0);

        // 优惠价
        BigDecimal discountUnitPrice = calculateDiscountPrice(originalUnitPrice, request.getCouponList());
        response.setDiscountedUnitPrice(discountUnitPrice);
        response.setDiscountedPrice(currentUsage.multiply(discountUnitPrice));

        response.setUsage(currentUsage);
        response.setChargeUnitCount(currentUsage);
        response.setChargeUsageCount(currentUsage);
        response.setOriginalPrice(currentUsage.multiply(originalUnitPrice));

        rateConfig.setOriginalUnitPrice(originalUnitPrice);
        rateConfig.setDiscountedUnitPrice(discountUnitPrice);
        rateConfig.setOriginalPrice(response.getOriginalPrice());
        rateConfig.setDiscountedPrice(response.getDiscountedPrice());
        rateConfig.setChargeUnitCount(currentUsage);
        rateConfig.setIsHit(true);

        calculateTax(request, response);

        response.setRateConfig(rateConfig);
        response.setCouponList(request.getCouponList());
        calculateProportion(request, response);

        response.setDiscountAmount(response.getOriginalPrice().subtract(response.getDiscountedPrice()));
        return response;
    }

}
