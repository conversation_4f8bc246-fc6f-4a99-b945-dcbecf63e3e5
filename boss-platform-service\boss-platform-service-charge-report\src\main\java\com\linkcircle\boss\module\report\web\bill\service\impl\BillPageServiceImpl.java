package com.linkcircle.boss.module.report.web.bill.service.impl;

import com.baomidou.mybatisplus.annotation.TableField;
import com.linkcircle.boss.framework.common.model.PageParam;
import com.linkcircle.boss.framework.common.model.PageResult;
import com.linkcircle.boss.framework.tanant.TenantUtils;
import com.linkcircle.boss.module.billing.api.bill.product.model.dto.BillDiscountConfigDTO;
import com.linkcircle.boss.module.billing.api.bill.product.model.entity.PostpaidProductServiceIncomeBillDO;
import com.linkcircle.boss.module.crm.api.customer.customer.vo.ChargeCustomerInfoVO;
import com.linkcircle.boss.module.crm.api.entity.vo.EntityDetailsVO;
import com.linkcircle.boss.module.crm.enums.BillTypeEnum;
import com.linkcircle.boss.module.report.enums.BillEnum;
import com.linkcircle.boss.module.report.web.bill.model.dto.BillQueryByCustomerIdsReqDTO;
import com.linkcircle.boss.module.report.web.bill.model.vo.BillPageVo;
import com.linkcircle.boss.module.report.web.bill.model.vo.makeup.MakeupIncomeBillPageVo;
import com.linkcircle.boss.module.report.web.bill.model.vo.postpaid.PostpaidIncomeProductBillDetailVO;
import com.linkcircle.boss.module.report.web.bill.model.vo.postpaid.PostpaidIncomeProductBillPageVO;
import com.linkcircle.boss.module.report.web.bill.model.vo.prepaid.PrepaidIncomeBillPageVO;
import com.linkcircle.boss.module.report.web.bill.service.BillPageService;
import com.linkcircle.boss.module.report.web.bill.service.MakeupBillService;
import com.linkcircle.boss.module.report.web.bill.service.PostpaidBillService;
import com.linkcircle.boss.module.report.web.bill.service.PrepaidBillService;
import com.linkcircle.boss.module.report.web.dynamic.util.PageHelper;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.IntStream;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2025/7/18 15:23
 */
@RequiredArgsConstructor
@Component
@Slf4j
public class BillPageServiceImpl implements BillPageService {


    private final MakeupBillService makeupBillService;
    private final PrepaidBillService prepaidBillService;
    private final PostpaidBillService postpaidBillService;





    @Override
    public PageResult<BillPageVo> queryBillPageByCustomerIds(BillQueryByCustomerIdsReqDTO pageReq) {
        int pageSize = pageReq.getPageSize();
        pageReq.setPageSize(1);
        PageResult<?> postpaidPageResult = TenantUtils.executeIgnore(() -> postpaidBillService.queryBillPageByCustomerIds(pageReq));
        PageResult<?> prepaidPageResult = TenantUtils.executeIgnore(() -> prepaidBillService.queryBillPageByCustomerIds(pageReq));
        PageResult<?> makeupPageResult = TenantUtils.executeIgnore(() -> makeupBillService.queryBillPageByCustomerIds(pageReq));
        long postpaidCount = postpaidPageResult.getTotal();
        long prepaidCount = prepaidPageResult.getTotal();
        long makeupCount = makeupPageResult.getTotal();
        // 初始化数据源（示例数据）
        List<PageHelper.Range> sources =  new ArrayList<>();
        long accumulateCount = 0;
        if (postpaidCount > 0) {
            sources.add(new PageHelper.Range(0, postpaidCount - 1, 0, postpaidCount - 1, pageSize, "后付费账单", pageParam -> postpaidBillService.queryBillPageByCustomerIds((BillQueryByCustomerIdsReqDTO) pageParam)));
            accumulateCount = postpaidCount;
        }
        if (prepaidCount > 0) {
            sources.add(new PageHelper.Range(0, prepaidCount - 1, accumulateCount, accumulateCount + prepaidCount - 1, pageSize, "预付费账单", pageParam -> prepaidBillService.queryBillPageByCustomerIds((BillQueryByCustomerIdsReqDTO) pageParam)));
           accumulateCount += prepaidCount;
        }
        if (makeupCount > 0) {
            sources.add(new PageHelper.Range(0, makeupCount - 1, accumulateCount, accumulateCount+makeupCount - 1, pageSize, "手工账单", pageParam -> makeupBillService.queryBillPageByCustomerIds((BillQueryByCustomerIdsReqDTO) pageParam)));
            accumulateCount += makeupCount;
        }
        if(Objects.equals(accumulateCount, 0L)){
            return PageResult.empty();
        }
        if(CollectionUtils.isEmpty(sources)){
            return PageResult.empty(accumulateCount);
        }
        pageReq.setPageSize(pageSize);
        return PageHelper.fetchPage(sources, pageReq,obj->{
            BillPageVo pageVo  = new BillPageVo();
            if(obj instanceof PostpaidIncomeProductBillDetailVO){
                POSTPAID_BILL_CONSUMER.accept(pageVo, (PostpaidIncomeProductBillDetailVO) obj);
            }else if(obj instanceof PrepaidIncomeBillPageVO){
                PREPAID_BILL_CONSUMER.accept(pageVo, (PrepaidIncomeBillPageVO) obj);
            }else if(obj instanceof MakeupIncomeBillPageVo){
                MAKEUP_INCOME_BILL_CONSUMER.accept(pageVo, (MakeupIncomeBillPageVo) obj);
            }
            return pageVo;
        });
    }
    private static  final BiConsumer<BillPageVo, PostpaidIncomeProductBillDetailVO> POSTPAID_BILL_CONSUMER = (pageVo, postpaid) -> {
        pageVo.setBillType(BillEnum.BillType.PREPAID.getCode());
        pageVo.setBillId(postpaid.getProductBillId());
        pageVo.setCustomerId(postpaid.getCustomerId());
        pageVo.setCustomerName( postpaid.getCustomerName());
        pageVo.setAccountId(    postpaid.getAccountId());
        pageVo.setAccountName(   postpaid.getAccountName());
        pageVo.setProductId(    postpaid.getProductId());
        pageVo.setProductName(  postpaid.getProductName());
        pageVo.setEntityId(    postpaid.getEntityId());
        pageVo.setEntityName(   postpaid.getEntityName());
        pageVo.setContractId(   postpaid.getContractId());
        pageVo.setContractName(  postpaid.getContractName());
        pageVo.setWalletId(    postpaid.getWalletId());
        pageVo.setWalletName(   postpaid.getWalletName());
        pageVo.setPlanId(postpaid.getPlanId());
        pageVo.setPlanName( postpaid.getPlanName());
        pageVo.setSubscribeId(postpaid.getSubscribeId());
        pageVo.setPaymentMethod(postpaid.getPaymentMethod());
        pageVo.setBillingType(postpaid.getBillingType());
        pageVo.setBillStatus(postpaid.getBillStatus());
        pageVo.setTaxRate(postpaid.getTaxRate());
        pageVo.setInvoicedAmount(postpaid.getInvoicedAmount());
        pageVo.setAvailableInvoiceAmount(postpaid.getAvailableInvoiceAmount());
        pageVo.setAmountWithTax(postpaid.getAmountWithTax());
        pageVo.setAmountWithoutTax(postpaid.getAmountWithoutTax());
        pageVo.setCurrency(postpaid.getCurrency());
        pageVo.setPaymentTime(postpaid.getPaymentTime());
        pageVo.setBillingTime(postpaid.getBillingTime());
        pageVo.setCreateTime(postpaid.getCreateTime());
        pageVo.setDeleted(false);
        pageVo.setProductNames(CollectionUtils.isEmpty(postpaid.getProductNames())?new ArrayList<>():postpaid.getProductNames().stream().filter(StringUtils::isNotBlank).toList());
        pageVo.setServiceNames(CollectionUtils.isEmpty(postpaid.getServiceNames())?new ArrayList<>():postpaid.getServiceNames().stream().filter(StringUtils::isNotBlank).toList());
        pageVo.setBillingStartTime(postpaid.getBillingStartTime());
        pageVo.setBillingEndTime(postpaid.getBillingEndTime());
        pageVo.setBillNo(postpaid.getBillNo());
        pageVo.setRefundInvoiceAmount(postpaid.getRefundInvoiceAmount());
    };
    private static  final BiConsumer<BillPageVo, MakeupIncomeBillPageVo> MAKEUP_INCOME_BILL_CONSUMER = (pageVo, makeup) -> {
        pageVo.setBillType(BillEnum.BillType.MAKEUP.getCode());
        pageVo.setBillId(makeup.getBillId());
        pageVo.setCustomerId(makeup.getCustomerId());
        pageVo.setCustomerName( makeup.getCustomerName());
        pageVo.setAccountId(    makeup.getAccountId());
        pageVo.setAccountName(   makeup.getAccountName());
        //pageVo.setProductId(    makeup.getProductId());
        //pageVo.setProductName(  makeup.getProductName());
        pageVo.setEntityId(    makeup.getEntityId());
        pageVo.setEntityName(   makeup.getEntityName());
        pageVo.setContractId(   makeup.getContractId());
        pageVo.setContractName(  makeup.getContractName());
        pageVo.setWalletId(    makeup.getWalletId());
        pageVo.setWalletName(   makeup.getWalletName());
       // pageVo.setPlanId(makeup.getPlanId());
        //pageVo.setPlanName( makeup.getPlanName());
        //pageVo.setSubscribeId(makeup.getSubscribeId());
        //pageVo.setPaymentMethod(makeup.getPaymentMethod());
        pageVo.setBillingType(makeup.getProducts().getFirst().getServices().getFirst().getBillingType());
        pageVo.setBillStatus(makeup.getBillStatus());
        pageVo.setTaxRate(makeup.getTaxRate());
        pageVo.setInvoicedAmount(makeup.getInvoicedAmount());
        pageVo.setAvailableInvoiceAmount(makeup.getAvailableInvoiceAmount());
        pageVo.setAmountWithTax(makeup.getAmountWithTax());
        pageVo.setAmountWithoutTax(makeup.getAmountWithoutTax());
        pageVo.setCurrency(makeup.getCurrency());
        pageVo.setPaymentTime(makeup.getPaymentTime());
        pageVo.setBillingTime(makeup.getBillingTime());
        pageVo.setCreateTime(makeup.getCreateTime());
        pageVo.setDeleted(false);
        pageVo.setProductNames(CollectionUtils.isEmpty(makeup.getProductNames())?new ArrayList<>():makeup.getProductNames().stream().filter(StringUtils::isNotBlank).toList());
        pageVo.setServiceNames(CollectionUtils.isEmpty(makeup.getServiceNames())?new ArrayList<>():makeup.getServiceNames().stream().filter(StringUtils::isNotBlank).toList());
        pageVo.setBillingStartTime(makeup.getBillingStartTime());
        pageVo.setBillingEndTime(makeup.getBillingEndTime());
        pageVo.setBillNo(makeup.getBillNo());
        pageVo.setRefundInvoiceAmount(makeup.getRefundInvoiceAmount());
    };

    private static  final BiConsumer<BillPageVo, PrepaidIncomeBillPageVO> PREPAID_BILL_CONSUMER = (pageVo, prepaid) -> {
        pageVo.setBillType(BillEnum.BillType.PREPAID.getCode());
        pageVo.setBillId(prepaid.getBillDetailId());
        pageVo.setCustomerId(prepaid.getCustomerId());
        pageVo.setCustomerName( prepaid.getCustomerName());
        pageVo.setAccountId(    prepaid.getAccountId());
        pageVo.setAccountName(   prepaid.getAccountName());
        pageVo.setProductId(    prepaid.getProductId());
        pageVo.setProductName(  prepaid.getProductName());
        pageVo.setEntityId(    prepaid.getEntityId());
        pageVo.setEntityName(   prepaid.getEntityName());
        pageVo.setContractId(   prepaid.getContractId());
        pageVo.setContractName(  prepaid.getContractName());
        pageVo.setWalletId(    prepaid.getWalletId());
        pageVo.setWalletName(   prepaid.getWalletName());
        pageVo.setPlanId(prepaid.getPlanId());
        pageVo.setPlanName( prepaid.getPlanName());
        pageVo.setSubscribeId(prepaid.getSubscribeId());
        pageVo.setPaymentMethod(prepaid.getPaymentMethod());
        pageVo.setBillingType(prepaid.getBillingType());
        pageVo.setBillStatus(prepaid.getBillStatus());
        pageVo.setTaxRate(prepaid.getTaxRate());
        pageVo.setInvoicedAmount(prepaid.getInvoicedAmount());
        pageVo.setAvailableInvoiceAmount(prepaid.getAvailableInvoiceAmount());
        pageVo.setAmountWithTax(prepaid.getAmountWithTax());
        pageVo.setAmountWithoutTax(prepaid.getAmountWithoutTax());
        pageVo.setCurrency(prepaid.getCurrency());
        pageVo.setPaymentTime(prepaid.getPaymentTime());
        pageVo.setBillingTime(prepaid.getBillingTime());
        pageVo.setCreateTime(prepaid.getCreateTime());
        pageVo.setDeleted(false);
        pageVo.setProductNames(Stream.of(StringUtils.isBlank(prepaid.getProductName())?"":prepaid.getProductName()).filter(StringUtils::isNotBlank).toList());
        pageVo.setServiceNames(Stream.of(StringUtils.isBlank(prepaid.getServiceName())?"":prepaid.getServiceName()).filter(StringUtils::isNotBlank).toList());
        pageVo.setBillingStartTime(prepaid.getBillingStartTime());
        pageVo.setBillingEndTime(prepaid.getBillingEndTime());
        pageVo.setBillNo(prepaid.getBillNo());
        pageVo.setRefundInvoiceAmount(prepaid.getRefundInvoiceAmount());
    };

    public static void main(String[] args) {
        Class<?> clazz = BillPageVo.class;
        String prefix = "pageVo.";
        Function<String, String> upperCase  = new Function<>() {
            @Override
            public String apply(String s) {
                return s.substring(0, 1).toUpperCase() + s.substring(1);
            }
        };
        for (Field declaredField : clazz.getDeclaredFields()) {
            String name = declaredField.getName();
            System.out.println(prefix +"set"+ upperCase.apply(name) + "();");
        }
    }
}
