package com.linkcircle.boss.module.charge.fee.web.bill.api;

import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.module.charge.fee.web.bill.service.BillFeeService;
import com.linkcircle.boss.module.fee.api.bill.BillFeeApi;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;

import static com.linkcircle.boss.framework.common.model.CommonResult.success;

/**
 * <AUTHOR>
 * @date 2025/7/17 9:31
 */
@Tag(name = "账单-计算-用于账单确认和创建账单页面的计算逻辑")
@RestController
@RequestMapping("/customer/bill-fee")
@Validated
@RequiredArgsConstructor
public class BillFeeApiController implements BillFeeApi {

    private final BillFeeService billFeeService;




    /**
     * 计算指定客户的未支付账单数量
     *
     * @param customerId 客户ID，必须参数
     * @return 包含未支付账单数量的 CommonResult 对象
     */
    public CommonResult<Long> computeCustomerUnPaidBillNum(@RequestParam(value = "customerId",required = true) Long customerId) {
        return success(billFeeService.computeCustomerUnPaidBillNum(customerId));
    }




    /**
     * 计算建造费用
     *
     * @param constructId 建造项目ID
     * @param startTime   开始时间（单位：毫秒），可选参数，默认值为当前时间
     * @param endTime     结束时间（单位：毫秒），可选参数，默认值为当前时间
     * @return 计算结果，包含建造费用
     */
    public CommonResult<BigDecimal> computeConstructFee(@RequestParam(value = "constructId",required = true) Long constructId,
                                                        @RequestParam(value = "startTime",required = false) Long startTime,
                                                        @RequestParam(value = "endTime",required = false) Long endTime) {
        return success(billFeeService.computeConstructFee(constructId,startTime,endTime));
    }



}
