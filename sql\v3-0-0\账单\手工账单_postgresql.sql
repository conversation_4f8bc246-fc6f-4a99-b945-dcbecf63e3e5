-- 手工账单-收入-账单表
DROP TABLE IF EXISTS `makeup_income_bill_${year}`;
CREATE TABLE makeup_income_bill_${year}
(
    bill_id                  BIGINT PRIMARY KEY,
    customer_id              BIGINT,
    account_id               BIGINT,
    entity_id                BIGINT,
    contract_id              BIGINT,
    wallet_id                BIGINT,
    bill_status              BIGINT CHECK (bill_status BETWEEN 0 AND 3),
    tax_rate                 NUMERIC(8, 4) CHECK (tax_rate >= 0 AND tax_rate <= 99.99),
    tax_amount               NUMERIC(18, 6),
    sub_total_amount         NUMERIC(18, 6),
    discount_amount          NUMERIC(18, 6),
    invoiced_amount          NUMERIC(18, 6),
    available_invoice_amount NUMERIC(18, 6),
    amount_with_tax          NUMERIC(18, 6),
    amount_without_tax       NUMERIC(18, 6),
    currency                 VARCHAR(3) CHECK (currency IN ('CNY', 'USD')),
    payment_time             BIGINT,
    billing_time             BIGINT,
    billing_start_time          BIGINT,
    billing_end_time            BIGINT,
    create_time              BIGINT,
    deleted                  SMALLINT DEFAULT 0 CHECK (deleted IN (0, 1)),
    discount_details         JSON,
    original_price           NUMERIC(18, 6),
    bill_code     varchar(50),
    refund_invoice_amount      NUMERIC(18, 6) ,
    `wallet_deduct_status`     tinyint        NULL DEFAULT 0
);

-- 主键注释
COMMENT ON COLUMN makeup_income_bill_${year}.bill_id IS '账单id';
COMMENT ON COLUMN makeup_income_bill_${year}.customer_id IS '客户id';
COMMENT ON COLUMN makeup_income_bill_${year}.account_id IS '账户id';
COMMENT ON COLUMN makeup_income_bill_${year}.entity_id IS '主体id';
COMMENT ON COLUMN makeup_income_bill_${year}.contract_id IS '合同id';
COMMENT ON COLUMN makeup_income_bill_${year}.wallet_id IS '钱包id';
COMMENT ON COLUMN makeup_income_bill_${year}.bill_status IS '账单状态 (0-草稿,1-待支付,2-已支付,3-未结清)';
COMMENT ON COLUMN makeup_income_bill_${year}.tax_rate IS '税率 (%)';
COMMENT ON COLUMN makeup_income_bill_${year}.tax_amount IS '税额';
COMMENT ON COLUMN makeup_income_bill_${year}.sub_total_amount IS '服务总价之和';
COMMENT ON COLUMN makeup_income_bill_${year}.discount_amount IS '账单优惠金额';
COMMENT ON COLUMN makeup_income_bill_${year}.invoiced_amount IS '已开票金额';
COMMENT ON COLUMN makeup_income_bill_${year}.available_invoice_amount IS '可开票金额';
COMMENT ON COLUMN makeup_income_bill_${year}.amount_with_tax IS '含税总金额';
COMMENT ON COLUMN makeup_income_bill_${year}.amount_without_tax IS '不含税金额';
COMMENT ON COLUMN makeup_income_bill_${year}.currency IS '货币单位 (CNY/USD)';
COMMENT ON COLUMN makeup_income_bill_${year}.payment_time IS '实际支付时间戳';
COMMENT ON COLUMN makeup_income_bill_${year}.billing_time IS '出账时间戳';
COMMENT ON COLUMN makeup_income_bill_${year}.billing_start_time IS '账期开始时间戳';
COMMENT ON COLUMN makeup_income_bill_${year}.billing_end_time IS '账期结束时间戳';
COMMENT ON COLUMN makeup_income_bill_${year}.create_time IS '数据创建时间戳';
COMMENT ON COLUMN makeup_income_bill_${year}.deleted IS '是否删除 (0-否,1-是)';
COMMENT ON COLUMN makeup_income_bill_${year}.discount_details IS '服务优惠详情 JSONB';
COMMENT ON COLUMN makeup_income_bill_${year}.original_price IS '目录价(原价)';

-- 索引定义
CREATE INDEX idx_makeup_income_bill_${year}_customer_id ON makeup_income_bill_${year} (customer_id);
COMMENT ON INDEX idx_makeup_income_bill_${year}_customer_id  IS '客户ID的Bitmap索引';

CREATE INDEX idx_makeup_income_bill_${year}_account_id ON makeup_income_bill_${year} (account_id);
COMMENT ON INDEX idx_makeup_income_bill_${year}_account_id  IS '账户ID的Bitmap索引';

CREATE INDEX idx_makeup_income_bill_${year}_entity_id ON makeup_income_bill_${year} (entity_id);
COMMENT ON INDEX idx_makeup_income_bill_${year}_entity_id  IS '主体ID的Bitmap索引';

CREATE INDEX idx_makeup_income_bill_${year}_contract_id ON makeup_income_bill_${year} (contract_id);
COMMENT ON INDEX idx_makeup_income_bill_${year}_contract_id  IS '合同ID的Bitmap索引';

CREATE INDEX idx_makeup_income_bill_${year}_billing_time ON makeup_income_bill_${year} (billing_time);
COMMENT ON INDEX idx_makeup_income_bill_${year}_billing_time  IS '账单时间的Bitmap索引';

CREATE INDEX idx_makeup_income_bill_${year}_bill_status ON makeup_income_bill_${year} (bill_status);
COMMENT ON INDEX idx_makeup_income_bill_${year}_bill_status  IS '账单状态的Bitmap索引';

CREATE INDEX idx_makeup_income_bill_${year}_bill_code ON makeup_income_bill_${year} (bill_code);
COMMENT ON INDEX idx_makeup_income_bill_${year}_bill_code  IS '账单号的Bitmap索引';


DROP TABLE IF EXISTS `makeup_product_income_bill_${year}`;
-- 手工账单-收入-产品账单表
CREATE TABLE makeup_product_income_bill_${year}
(
    product_bill_id    BIGINT PRIMARY KEY,
    bill_id            BIGINT,
    customer_id        BIGINT,
    account_id         BIGINT,
    entity_id          BIGINT,
    contract_id        BIGINT,
    wallet_id          BIGINT,
    product_id         BIGINT,
    billing_time       BIGINT,
    create_time        BIGINT,
    deleted            BIGINT DEFAULT 0 CHECK (deleted IN (0, 1)),
    discount_details   JSON,
    original_price     NUMERIC(18, 6),
    discount_amount    NUMERIC(18, 6),
    amount_with_tax    NUMERIC(18, 6),
    amount_without_tax NUMERIC(18, 6),
    currency           VARCHAR(3) ,
    tax_amount         NUMERIC(18, 6),
    tax_rate           NUMERIC(8, 4) CHECK (tax_rate >= 0 AND tax_rate <= 99.99)
);

-- 主键注释
COMMENT ON COLUMN makeup_product_income_bill_${year}.product_bill_id IS '产品账单id';
COMMENT ON COLUMN makeup_product_income_bill_${year}.bill_id IS '账单ID';
COMMENT ON COLUMN makeup_product_income_bill_${year}.customer_id IS '客户id';
COMMENT ON COLUMN makeup_product_income_bill_${year}.account_id IS '账户id';
COMMENT ON COLUMN makeup_product_income_bill_${year}.entity_id IS '主体id';
COMMENT ON COLUMN makeup_product_income_bill_${year}.contract_id IS '合同id';
COMMENT ON COLUMN makeup_product_income_bill_${year}.wallet_id IS '钱包id';
COMMENT ON COLUMN makeup_product_income_bill_${year}.product_id IS '产品id';
COMMENT ON COLUMN makeup_product_income_bill_${year}.billing_time IS '出账时间戳';
COMMENT ON COLUMN makeup_product_income_bill_${year}.create_time IS '数据创建时间戳';
COMMENT ON COLUMN makeup_product_income_bill_${year}.deleted IS '是否删除 (0-否,1-是)';
COMMENT ON COLUMN makeup_product_income_bill_${year}.discount_details IS '服务优惠详情 JSONB';
COMMENT ON COLUMN makeup_product_income_bill_${year}.original_price IS '目录价(原价)';
COMMENT ON COLUMN makeup_product_income_bill_${year}.discount_amount IS '优惠金额';
COMMENT ON COLUMN makeup_product_income_bill_${year}.amount_with_tax IS '含税总金额';
COMMENT ON COLUMN makeup_product_income_bill_${year}.amount_without_tax IS '不含税金额';
COMMENT ON COLUMN makeup_product_income_bill_${year}.currency IS '货币单位 (CNY/USD)';
COMMENT ON COLUMN makeup_product_income_bill_${year}.tax_amount IS '税额';
COMMENT ON COLUMN makeup_product_income_bill_${year}.tax_rate IS '税率 (%)';

-- 索引定义
CREATE INDEX idx_makeup_product_income_bill_${year}_customer_id_product ON makeup_product_income_bill_${year} (customer_id);
COMMENT ON INDEX idx_makeup_product_income_bill_${year}_customer_id_product  IS '客户ID的Bitmap索引';

CREATE INDEX idx_makeup_product_income_bill_${year}_account_id_product ON makeup_product_income_bill_${year} (account_id);
COMMENT ON INDEX idx_makeup_product_income_bill_${year}_account_id_product IS '账户ID的Bitmap索引';

CREATE INDEX idx_makeup_product_income_bill_${year}_billing_time_product ON makeup_product_income_bill_${year} (billing_time);
COMMENT ON INDEX idx_makeup_product_income_bill_${year}_billing_time_product  IS '账单时间的Bitmap索引';

CREATE INDEX idx_makeup_product_income_bill_${year}_product_id ON makeup_product_income_bill_${year} (product_id);
COMMENT ON INDEX idx_makeup_product_income_bill_${year}_product_id  IS '产品ID的Bitmap索引';

CREATE INDEX idx_makeup_product_income_bill_${year}_bill_id_product ON makeup_product_income_bill_${year} (bill_id);
COMMENT ON INDEX idx_makeup_product_income_bill_${year}_bill_id_product  IS '账单ID的Bitmap索引';

-- 手工账单-收入-产品服务账单表
DROP TABLE IF EXISTS `makeup_product_service_income_bill_${year}`;
CREATE TABLE makeup_product_service_income_bill_${year}
(
    product_service_bill_id BIGINT PRIMARY KEY,
    product_bill_id         BIGINT,
    bill_id                 BIGINT,
    entity_id               BIGINT,
    contract_id             BIGINT,
    customer_id             BIGINT,
    account_id              BIGINT,
    wallet_id               BIGINT,
    product_id              BIGINT,
    service_id              BIGINT,
    usage_count             NUMERIC(18, 6),
    usage_unit              VARCHAR(16),
    tax_rate                NUMERIC(5, 4),
    original_price          NUMERIC(18, 6),
    discount_amount         NUMERIC(18, 6),
    amount_with_tax         NUMERIC(18, 6),
    amount_without_tax      NUMERIC(18, 6),
    currency                VARCHAR(3),
    billing_time            BIGINT,
    create_time             BIGINT,
    deleted                 smallint,
    rate_details            JSON,
    discount_details        JSON,
    billing_type            SMALLINT
);

-- 添加列注释
COMMENT ON COLUMN makeup_product_service_income_bill_${year}.product_service_bill_id IS '产品服务账单id';
COMMENT ON COLUMN makeup_product_service_income_bill_${year}.product_bill_id IS '产品账单id';
COMMENT ON COLUMN makeup_product_service_income_bill_${year}.bill_id IS '账单id';
COMMENT ON COLUMN makeup_product_service_income_bill_${year}.entity_id IS '主体id';
COMMENT ON COLUMN makeup_product_service_income_bill_${year}.contract_id IS '合同id';
COMMENT ON COLUMN makeup_product_service_income_bill_${year}.customer_id IS '客户id';
COMMENT ON COLUMN makeup_product_service_income_bill_${year}.account_id IS '账户id';
COMMENT ON COLUMN makeup_product_service_income_bill_${year}.wallet_id IS '钱包id';
COMMENT ON COLUMN makeup_product_service_income_bill_${year}.product_id IS '产品id';
COMMENT ON COLUMN makeup_product_service_income_bill_${year}.service_id IS '服务id';
COMMENT ON COLUMN makeup_product_service_income_bill_${year}.usage_count IS '消耗量';
COMMENT ON COLUMN makeup_product_service_income_bill_${year}.usage_unit IS '消耗量单位';
COMMENT ON COLUMN makeup_product_service_income_bill_${year}.tax_rate IS '税率';
COMMENT ON COLUMN makeup_product_service_income_bill_${year}.original_price IS '目录价(原价)';
COMMENT ON COLUMN makeup_product_service_income_bill_${year}.discount_amount IS '优惠的金额';
COMMENT ON COLUMN makeup_product_service_income_bill_${year}.amount_with_tax IS '含税总金额';
COMMENT ON COLUMN makeup_product_service_income_bill_${year}.amount_without_tax IS '不含税金额';
COMMENT ON COLUMN makeup_product_service_income_bill_${year}.currency IS '货币单位 (CNY/USD)';
COMMENT ON COLUMN makeup_product_service_income_bill_${year}.billing_time IS '出账时间戳';
COMMENT ON COLUMN makeup_product_service_income_bill_${year}.create_time IS '数据创建时间戳';
COMMENT ON COLUMN makeup_product_service_income_bill_${year}.deleted IS '是否删除 (0-否,1-是)';
COMMENT ON COLUMN makeup_product_service_income_bill_${year}.rate_details IS '费用详情json 参见 billing-api com.linkcircle.boss.module.billing.api.rate.model.dto.*';
COMMENT ON COLUMN makeup_product_service_income_bill_${year}.discount_details IS '服务优惠详情json 参见 List<com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.Coupon>';
COMMENT ON COLUMN makeup_product_service_income_bill_${year}.billing_type IS '计费类型 (0-固定费率,1-阶梯费率,2-套餐计费,3-按量计费)';

-- 创建索引并添加注释
CREATE INDEX idx_makeup_product_service_income_bill_${year}_customer_id ON makeup_product_service_income_bill_${year} (customer_id);
COMMENT ON INDEX idx_makeup_product_service_income_bill_${year}_customer_id IS '客户ID的Bitmap索引';

CREATE INDEX idx_makeup_product_service_income_bill_${year}_account_id ON makeup_product_service_income_bill_${year} (account_id);
COMMENT ON INDEX idx_makeup_product_service_income_bill_${year}_account_id IS '账户ID的Bitmap索引';

CREATE INDEX idx_makeup_product_service_income_bill_${year}_billing_time ON makeup_product_service_income_bill_${year} (billing_time);
COMMENT ON INDEX idx_makeup_product_service_income_bill_${year}_billing_time IS '账单时间的Bitmap索引';

CREATE INDEX idx_makeup_product_service_income_bill_${year}_product_id ON makeup_product_service_income_bill_${year} (product_id);
COMMENT ON INDEX idx_makeup_product_service_income_bill_${year}_product_id IS '产品ID的Bitmap索引';

CREATE INDEX idx_makeup_product_service_income_bill_${year}_service_id ON makeup_product_service_income_bill_${year} (service_id);
COMMENT ON INDEX idx_makeup_product_service_income_bill_${year}_service_id IS '服务ID的Bitmap索引';

CREATE INDEX idx_makeup_product_service_income_bill_${year}_bill_id ON makeup_product_service_income_bill_${year} (bill_id);
COMMENT ON INDEX idx_makeup_product_service_income_bill_${year}_bill_id IS '账单ID的Bitmap索引';

CREATE INDEX idx_makeup_product_service_income_bill_${year}_product_bill_id ON makeup_product_service_income_bill_${year} (product_bill_id);
COMMENT ON INDEX idx_makeup_product_service_income_bill_${year}_product_bill_id IS '产品账单ID的Bitmap索引';