<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.linkcircle.boss.module.charge.crm.web.plan.mapper.ChargePlanMapper">

    <select id="queryByPage"
            resultType="com.linkcircle.boss.module.charge.crm.web.plan.model.vo.ChargePlanPageVO">
        SELECT
        t.id,
        t.plan_name as planName,
        t.currency_code as currencyCode,
        t.payment_type as paymentType,
        t.status,
        t.create_time as createTime,
        t.creator,
        t.remark
        FROM charge_plan t
        <where>
            t.deleted = 0
            <if test="query.planName != null and query.planName != ''">
                AND t.plan_name LIKE CONCAT('%',#{query.planName},'%')
            </if>
            <if test="query.status != null">
                AND t.status = #{query.status}
            </if>
            <if test="query.paymentType != null">
                AND t.payment_type = #{query.paymentType}
            </if>
        </where>
        ORDER BY t.create_time DESC
    </select>

</mapper>