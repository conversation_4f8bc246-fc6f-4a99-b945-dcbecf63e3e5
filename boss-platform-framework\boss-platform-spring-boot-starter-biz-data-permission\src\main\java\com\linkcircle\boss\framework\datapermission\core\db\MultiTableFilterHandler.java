package com.linkcircle.boss.framework.datapermission.core.db;

import com.baomidou.mybatisplus.extension.plugins.handler.MultiDataPermissionHandler;
import com.linkcircle.boss.framework.datapermission.core.annotation.UserDataPermission;
import com.linkcircle.boss.framework.datapermission.core.aop.UserDataPermissionContextHolder;
import com.linkcircle.boss.framework.datapermission.core.user.TableUserPermissionConfig;
import com.linkcircle.boss.framework.datapermission.core.user.UserDataPermissionHandler;
import com.linkcircle.boss.framework.datapermission.core.user.UserDataPermissionVO;
import com.linkcircle.boss.module.system.enums.permission.UserDataScopeEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.LongValue;
import net.sf.jsqlparser.expression.operators.conditional.AndExpression;
import net.sf.jsqlparser.expression.operators.relational.EqualsTo;
import net.sf.jsqlparser.expression.operators.relational.ExpressionList;
import net.sf.jsqlparser.expression.operators.relational.InExpression;
import net.sf.jsqlparser.expression.operators.relational.ParenthesedExpressionList;
import net.sf.jsqlparser.schema.Column;
import net.sf.jsqlparser.schema.Table;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;

/**
 * 多表数据权限过滤处理器
 * 实现 MultiDataPermissionHandler 接口，用于处理多表数据权限过滤逻辑
 */
@Component
@Slf4j
public class MultiTableFilterHandler implements MultiDataPermissionHandler {

    @Resource
    private UserDataPermissionHandler permissionService;

    /**
     * 表名到权限配置的映射
     */
    private final Map<String, TableUserPermissionConfig> TABLE_PERMISSION_MAP = new ConcurrentHashMap<>();

    /**
     * 构造函数，用于初始化 TABLE_PERMISSION_MAP
     *
     * <p>在构造函数中，我们初始化了 TABLE_PERMISSION_MAP，该映射表用于存储不同表的权限配置。
     * 每个表都有一个对应的 {@link TableUserPermissionConfig} 对象，该对象定义了表的权限配置。
     * 这里我们为 "charge_customer_info", "charge_supplier_info", "contract_info" 和 "project_info" 四个表
     * 分别设置了权限配置，每个配置都指定了表的唯一标识符（如 "id"）以及一个用于获取用户对应权限的方法（如
     * {@link UserDataPermissionVO#getCustomerIds()}）。
     */
    public MultiTableFilterHandler() {
        // 初始化表权限配置
        TABLE_PERMISSION_MAP.put("charge_customer_info", new TableUserPermissionConfig("id", UserDataPermissionVO::getCustomerIds));
        TABLE_PERMISSION_MAP.put("charge_supplier_info", new TableUserPermissionConfig("id", UserDataPermissionVO::getSupplierIds));
        TABLE_PERMISSION_MAP.put("contract_info", new TableUserPermissionConfig("id", UserDataPermissionVO::getContractIds));
        TABLE_PERMISSION_MAP.put("project_info", new TableUserPermissionConfig("id", UserDataPermissionVO::getProjectIds));
    }

    /**
     * 根据给定的表、条件表达式和映射语句ID，获取相应的SQL片段。
     *
     * @param table            表示数据库表的实体对象
     * @param where            表示SQL查询的条件表达式
     * @param mappedStatementId 映射语句的ID
     * @return 返回包含权限过滤条件的SQL片段
     */
    @Override
    public Expression getSqlSegment(Table table, Expression where, String mappedStatementId) {
        // 检查是否跳过权限拦截
        UserDataPermission permission = UserDataPermissionContextHolder.current();
        if (permission != null && !permission.enable()) {
            return where; // 跳过检查
        }

        // 获取表名（小写）
        String tableName = table.getName().toLowerCase();

        // 检查是否需要处理该表
        if (!TABLE_PERMISSION_MAP.containsKey(tableName)) {
            return where;
        }

        // 获取表权限配置
        TableUserPermissionConfig config = TABLE_PERMISSION_MAP.get(tableName);
        String columnName = config.getColumnName();
        Function<UserDataPermissionVO, List<Long>> idListFunction = config.getIdListFunction();

        // 获取当前用户的数据权限
        UserDataPermissionVO permissionVO = permissionService.getCurrentUserPermission();
        if (permissionVO == null) {
            return addNoPermissionCondition(where);
        }

        // 全部数据权限直接返回
        if (UserDataScopeEnum.ALL.getScope().equals(permissionVO.getDataScope())) {
            return where;
        }

        // 获取权限ID列表
        List<Long> ids = idListFunction.apply(permissionVO);
        if (CollectionUtils.isEmpty(ids)) {
            return addNoPermissionCondition(where);
        }

        // 当前用户拥有此表的所有数据权限,直接返回
        if (ids.getFirst().equals(-1L)) {
            return where;
        }

        // 使用表别名或原始表名
        String tableAlias = (table.getAlias() != null) ?
                table.getAlias().getName().toLowerCase() :
                tableName;

        // 构建IN条件（使用动态列名）
        return addIdInCondition(where, tableAlias, columnName, ids);
    }

    /**
     * 为给定的条件添加ID在列表中的条件
     *
     * @param where         现有的条件表达式
     * @param tableAlias    表的别名
     * @param columnName    列名
     * @param ids           ID列表
     * @return              返回新的条件表达式
     */
    private Expression addIdInCondition(Expression where, String tableAlias,
                                        String columnName, List<Long> ids) {
        // 构建列表达式: tableAlias.columnName
        Column column = new Column(tableAlias + "." + columnName);

        // 构建IN表达式
        InExpression inExpression = new InExpression();
        inExpression.setLeftExpression(column);

        // 创建带括号的表达式列表
        ExpressionList expressionList = new ExpressionList();
        for (Long id : ids) {
            expressionList.addExpression(new LongValue(id));
        }
        inExpression.setRightExpression(new ParenthesedExpressionList(expressionList));

        // 组合现有条件
        return (where == null) ? inExpression : new AndExpression(where, inExpression);
    }

    /**
     * 为给定的 WHERE 条件表达式添加一个永远为假的条件（1 = 0）。
     *
     * @param where 原始的 WHERE 条件表达式
     * @return 如果 where 为 null，则直接返回永远为假的条件表达式；
     *         否则，返回将原始条件与永远为假的条件通过 AND 连接后的新表达式
     */
    private Expression addNoPermissionCondition(Expression where) {
        // 添加一个永远为假的条件: 1 = 0
        EqualsTo noPermission = new EqualsTo();
        noPermission.setLeftExpression(new LongValue(1));
        noPermission.setRightExpression(new LongValue(0));

        return (where == null) ? noPermission : new AndExpression(where, noPermission);
    }
}