package com.linkcircle.boss.module.charge.fee.web.bill.service.impl.fee.makeup;


import cn.hutool.core.util.IdUtil;
import com.linkcircle.boss.framework.common.exception.util.ServiceExceptionUtil;
import com.linkcircle.boss.module.charge.fee.constants.ErrorCodeConstants;
import com.linkcircle.boss.module.charge.fee.enums.InvoiceEnum;
import com.linkcircle.boss.module.charge.fee.web.bill.chain.InterceptorHandler;
import com.linkcircle.boss.module.charge.fee.web.bill.mapper.MakeupBillMapper;
import com.linkcircle.boss.module.charge.fee.web.bill.model.dto.calculate.BillFeeCalculateReqDTO;
import com.linkcircle.boss.module.charge.fee.web.bill.model.dto.calculate.BillProductServiceFeeReq;
import com.linkcircle.boss.module.charge.fee.web.bill.model.entity.MakeupIncomeBillDO;
import com.linkcircle.boss.module.charge.fee.web.bill.model.entity.MakeupProductIncomeBillDO;
import com.linkcircle.boss.module.charge.fee.web.bill.model.entity.MakeupProductServiceIncomeBillDO;
import com.linkcircle.boss.module.charge.fee.web.bill.model.vo.calculate.BillFeeResponseVO;
import com.linkcircle.boss.module.charge.fee.web.bill.model.vo.calculate.MakeupBillVO;
import com.linkcircle.boss.module.charge.fee.web.bill.service.BillIdGenerator;
import com.linkcircle.boss.module.charge.fee.web.bill.service.impl.fee.common.JsonApi;
import com.linkcircle.boss.module.charge.fee.web.bill.service.impl.fee.fixed.FixedCalculatePriceHandler;
import com.linkcircle.boss.module.crm.api.customer.account.vo.CustomerAccountVO;
import com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.Coupon;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/7/17 12:49
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MakeupBillHandler extends InterceptorHandler<BillFeeCalculateReqDTO, MakeupBillVO> implements JsonApi {


    private final FixedCalculatePriceHandler fixedCalculatePriceHandler;

    private final MakeupBillMapper makeupBillMapper;

    private final BillIdGenerator billIdGenerator;


    @Override
    protected MakeupBillVO doPreHandle(BillFeeCalculateReqDTO request, MakeupBillVO bp) {
        MakeupBillVO MakeupBillVO = new MakeupBillVO();
        MakeupBillVO.setUpdate(request.getCreateBillInfo()==null);
        if (MakeupBillVO.isUpdate()) {
            updateBill(request, MakeupBillVO);
        } else {
            createBill(request, MakeupBillVO);
        }
        return MakeupBillVO;
    }

    private void updateBill(BillFeeCalculateReqDTO request, MakeupBillVO makeupBillDTO) {
        List<MakeupIncomeBillDO> makeupIncomeBillDOS = makeupBillMapper.queryBillByIds(List.of(request.getUpdateBillInfo().getBillId()), request.getUpdateBillInfo().getBillingTime(), request.getUpdateBillInfo().getBillingTime());
        if (CollectionUtils.isEmpty(makeupIncomeBillDOS)) {
            log.info("手工账单不存在:{}", request.getUpdateBillInfo().getBillId());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.MAKEUP_INCOME_BILL_NOT_EXIST);
        }
        makeupBillDTO.setBill(makeupIncomeBillDOS.getFirst());
        MakeupIncomeBillDO bill = makeupBillDTO.getBill();
        Optional<BillProductServiceFeeReq> productFeeReq = request.getProductFees().stream().flatMap(t -> t.getProductServiceFees().stream()).findFirst();
        BillFeeCalculateReqDTO.UpdateBillInfo billInfo = request.getUpdateBillInfo();
        bill.setCustomerId(bill.getCustomerId());
        bill.setAccountId(bill.getAccountId());
        bill.setEntityId(request.getUpdateBillInfo().getEntityId());
        bill.setContractId(bill.getContractId());
        bill.setWalletId(bill.getWalletId());
        bill.setBillStatus(bill.getBillStatus());
        bill.setTaxRate(productFeeReq.get().getTaxRate());
        bill.setTaxAmount(BigDecimal.ZERO);
        bill.setSubTotalAmount(BigDecimal.ZERO);
        bill.setDiscountAmount(BigDecimal.ZERO);
        bill.setInvoicedAmount(BigDecimal.ZERO);
        bill.setAvailableInvoiceAmount(BigDecimal.ZERO);
        bill.setAmountWithTax(BigDecimal.ZERO);
        bill.setAmountWithoutTax(BigDecimal.ZERO);
        bill.setBillingTime(billInfo.getBillingTime());
        bill.setBillingStartTime(bill.getBillingStartTime());
        bill.setBillingEndTime(bill.getBillingEndTime());
        bill.setDiscountDetails(toStr(request.getCouponList(), t -> Objects.equals(t.getApplyTo(), 2)));
    }

    private void createBill(BillFeeCalculateReqDTO request, MakeupBillVO makeupBillDTO) {
        MakeupIncomeBillDO bill = new MakeupIncomeBillDO();
        CustomerAccountVO accountVO = request.getExtInfo();

        BillFeeCalculateReqDTO.CreateBillInfo billInfo = request.getCreateBillInfo();
        bill.setBillId(IdUtil.getSnowflakeNextId());
        bill.setBillNo(billIdGenerator.generateId(request.getCreateBillInfo().getEntityId(), InvoiceEnum.BillType.MAKEUP.getCode()));
        bill.setRefundInvoiceAmount(BigDecimal.ZERO);
        bill.setCustomerId(billInfo.getCustomerId());
        bill.setAccountId(billInfo.getAccountId());
        bill.setEntityId(billInfo.getEntityId());
        bill.setContractId(0L);
        bill.setWalletId(billInfo.getWalletId());
        bill.setBillStatus(InvoiceEnum.BillStatus.WAIT_PAYMENT.getCode());
        bill.setTaxRate(request.getProductFees().getFirst().getProductServiceFees().getFirst().getTaxRate());
        bill.setTaxAmount(BigDecimal.ZERO);
        bill.setSubTotalAmount(BigDecimal.ZERO);
        bill.setDiscountAmount(BigDecimal.ZERO);
        bill.setInvoicedAmount(BigDecimal.ZERO);
        bill.setAvailableInvoiceAmount(BigDecimal.ZERO);
        bill.setAmountWithTax(BigDecimal.ZERO);
        bill.setAmountWithoutTax(BigDecimal.ZERO);
        bill.setPaidAmount(BigDecimal.ZERO);
        bill.setUnpaidAmount(bill.getAmountWithTax());
        bill.setCurrency(accountVO.getCurrency());
        bill.setPaymentTime(0L);
        bill.setBillingTime(System.currentTimeMillis());
        bill.setBillingStartTime(billInfo.getBillingStartTime());
        bill.setBillingEndTime(billInfo.getBillingEndTime());
        bill.setCreateTime(System.currentTimeMillis());
        bill.setDeleted(0);
        bill.setDiscountDetails(toStr(request.getCouponList(), t -> Objects.equals(t.getApplyTo(), 2)));
        makeupBillDTO.setBillType(InvoiceEnum.BillType.MAKEUP.getCode());
        makeupBillDTO.setBill(bill);
    }

    @Override
    protected MakeupBillVO doPostHandle(BillFeeCalculateReqDTO request, MakeupBillVO makeupBillDTO) {
        MakeupIncomeBillDO bill = makeupBillDTO.getBill();
        bill.setCurrency(makeupBillDTO.getProducts().getFirst().getCurrency());
        // 计算账单优惠总和
        bill.setOriginalPrice(sumProductOriginalPrice(makeupBillDTO.getProducts()));
        bill.setDiscountAmount(fixedCalculatePriceHandler.calculateDiscountPrice(bill.getOriginalPrice(), toList(bill.getDiscountDetails(), Coupon.class)));
        bill.setSubTotalAmount(sumServiceTotalAmount(makeupBillDTO.getServices()));
        bill.setAmountWithoutTax(bill.getOriginalPrice().subtract(bill.getDiscountAmount()));
        bill.setTaxRate(makeupBillDTO.getProducts().getFirst().getTaxRate());
        bill.setTaxAmount(bill.getTaxRate().multiply(bill.getAmountWithoutTax()));
        bill.setAmountWithoutTax(bill.getAmountWithoutTax().add(bill.getTaxAmount()));
        bill.setInvoicedAmount(BigDecimal.ZERO);
        bill.setAvailableInvoiceAmount(bill.getAmountWithoutTax());


        BillFeeResponseVO billFeeResponse = makeupBillDTO.getBillFeeResponse();
        billFeeResponse.setDiscountAmount(bill.getDiscountAmount());
        billFeeResponse.setSubTotalAmount(bill.getSubTotalAmount());
        billFeeResponse.setTaxAmount(bill.getTaxAmount());
        billFeeResponse.setAmountWithoutTax(bill.getAmountWithoutTax());
        billFeeResponse.setAmountWithTax(bill.getAmountWithTax());
        billFeeResponse.setAvailableInvoiceAmount(bill.getAvailableInvoiceAmount());
        billFeeResponse.setInvoicedAmount(bill.getInvoicedAmount());
        billFeeResponse.setBillId(bill.getBillId());
        billFeeResponse.setTaxRate(bill.getTaxRate());
        billFeeResponse.setBillType(makeupBillDTO.getBillType());
        return makeupBillDTO;
    }

    private BigDecimal sumServiceTotalAmount(List<MakeupProductServiceIncomeBillDO> services) {
        BigDecimal subTotalAmount = BigDecimal.ZERO;
        for (MakeupProductServiceIncomeBillDO service : services) {
            subTotalAmount = subTotalAmount.add(service.getAmountWithTax());
        }
        return subTotalAmount;
    }

    private BigDecimal sumProductOriginalPrice(List<MakeupProductIncomeBillDO> products) {
        BigDecimal amountWithoutTax = BigDecimal.ZERO;
        for (MakeupProductIncomeBillDO product : products) {
            amountWithoutTax = amountWithoutTax.add(product.getAmountWithoutTax());
        }
        return amountWithoutTax;
    }
}
