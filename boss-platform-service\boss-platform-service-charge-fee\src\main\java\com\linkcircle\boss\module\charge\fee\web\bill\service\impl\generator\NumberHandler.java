package com.linkcircle.boss.module.charge.fee.web.bill.service.impl.generator;

import com.linkcircle.boss.module.charge.fee.enums.BusinessCodeEnum;
import com.linkcircle.boss.module.charge.fee.web.bill.chain.EnhanceHandler;
import com.linkcircle.boss.module.crm.api.basicConfig.vo.InvoiceDetailsVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2025/7/23 16:55
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class NumberHandler  extends EnhanceHandler<InvoiceDetailsVO, String> {

    private static final String regex = "\\{number_\\d+\\}";

    private final SequenceGenerator sequenceGenerator;



    @Override
    protected String doHandle(InvoiceDetailsVO invoiceDetailsVO, String message) {
        if (message == null) {
            message = invoiceDetailsVO.getNumberFormat();
        }
        if (StringUtils.isNotBlank(message)) {
            Pattern pattern = Pattern.compile(regex);
            Matcher matcher = pattern.matcher(message);
            while (matcher.find()) {
                String group = matcher.group();
                String number = group.substring("{number_".length(), group.length() - 1);
                int num = Integer.parseInt(number);
                String format = "%0" + num + "d";
                String replace = String.format(format, sequenceGenerator.incrementAndGet(chooseBusinessCode(invoiceDetailsVO),invoiceDetailsVO.getStartSequence(), invoiceDetailsVO.getNumberInit()));
                message = message.replace(group, replace);
            }
        }
        return message;
    }

    private String chooseBusinessCode(InvoiceDetailsVO invoiceDetailsVO) {
        if (invoiceDetailsVO!=null && Objects.equals(invoiceDetailsVO.getType(),1)) {
            return BusinessCodeEnum.BILL_ID_GENERATE.getCode();
        }
        return BusinessCodeEnum.BILL_INVOICE_ID_GENERATE.getCode();
    }
}
