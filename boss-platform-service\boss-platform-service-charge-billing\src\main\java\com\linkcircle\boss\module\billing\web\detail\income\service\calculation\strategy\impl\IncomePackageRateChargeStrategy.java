package com.linkcircle.boss.module.billing.web.detail.income.service.calculation.strategy.impl;

import cn.hutool.core.lang.Pair;
import com.linkcircle.boss.module.billing.api.rate.model.dto.PackageRateConfigDTO;
import com.linkcircle.boss.module.billing.constants.ErrorCodeConstants;
import com.linkcircle.boss.module.billing.constants.RateTypeConstant;
import com.linkcircle.boss.module.billing.enums.IncomeRateTypeEnum;
import com.linkcircle.boss.module.billing.enums.OriginalPriceRateTypeEnum;
import com.linkcircle.boss.module.billing.exception.BillingFailException;
import com.linkcircle.boss.module.billing.web.data.model.vo.CyclePeriodResultVO;
import com.linkcircle.boss.module.billing.web.data.service.CyclePeriodCalculateService;
import com.linkcircle.boss.module.billing.web.data.service.RateCacheDataService;
import com.linkcircle.boss.module.billing.web.detail.income.model.dto.ServiceSubscriptionInfoDTO;
import com.linkcircle.boss.module.billing.web.detail.income.service.calculation.strategy.AbstractIncomeRateChargeStrategy;
import com.linkcircle.boss.module.billing.web.detail.income.service.calculation.strategy.context.IncomeRateChargeRequest;
import com.linkcircle.boss.module.billing.web.detail.income.service.calculation.strategy.context.IncomeRateChargeResponse;
import com.linkcircle.boss.module.billing.web.detail.rate.income.service.IncomeRateUsageManageService;
import com.linkcircle.boss.module.billing.web.detail.rate.strategy.context.OriginalPriceCalculateRequest;
import com.linkcircle.boss.module.billing.web.detail.rate.strategy.context.OriginalPriceCalculateResponse;
import com.linkcircle.boss.module.crm.api.customer.account.vo.CustomerAccountVO;
import com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.AccountSubscriptionsVO;
import com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.Coupon;
import com.linkcircle.boss.module.crm.enums.ChargeRateTypeEnum;
import io.github.kk01001.design.pattern.strategy.IStrategy;
import io.github.kk01001.design.pattern.strategy.StrategyFactory;
import io.github.kk01001.design.pattern.strategy.annotation.Strategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-06-19 15:16
 * @description 套餐计费策略
 */
@Slf4j
@Component
@RequiredArgsConstructor
@Strategy(strategyEnum = IncomeRateTypeEnum.class, strategyType = RateTypeConstant.PACKAGE)
public class IncomePackageRateChargeStrategy extends AbstractIncomeRateChargeStrategy implements IStrategy<IncomeRateChargeRequest, IncomeRateChargeResponse> {

    private final RateCacheDataService rateCacheDataService;
    private final CyclePeriodCalculateService cyclePeriodCalculateService;
    private final IncomeRateUsageManageService incomeRateUsageManageService;
    private final StrategyFactory strategyFactory;

    @Override
    protected CyclePeriodCalculateService getCyclePeriodCalculateService() {
        return cyclePeriodCalculateService;
    }

    @Override
    public IncomeRateChargeResponse execute(IncomeRateChargeRequest request) {
        log.info("开始执行套餐计费策略");

        // 1. 获取基础数据
        ServiceSubscriptionInfoDTO serviceInfo = request.getServiceSubscriptionInfoDTO();
        CustomerAccountVO customerAccountVO = request.getCustomerAccountVO();
        String accountCurrency = customerAccountVO.getCurrency();
        AccountSubscriptionsVO.Service serviceConfig = serviceInfo.getServiceConfig();
        BigDecimal usage = request.getUsage();

        // 2. 解析套餐计费配置
        Pair<PackageRateConfigDTO, PackageRateConfigDTO.PackageConfigDTO> packageRateConfigPair
                = getPackageRateConfigDTO(serviceConfig, accountCurrency);
        PackageRateConfigDTO packageRateConfigDTO = packageRateConfigPair.getKey();
        PackageRateConfigDTO.PackageConfigDTO packageConfigDTO = packageRateConfigPair.getValue();

        // 4. 计算计费周期和设置计费锁
        Long businessTime = request.getBusinessTime();
        Long subscriptionId = serviceInfo.getSubscriptionsVO().getId();
        CyclePeriodResultVO cyclePeriodResultVO = getBillingCycle(serviceInfo, customerAccountVO, businessTime);

        // 5. 获取当前周期累计用量
        BigDecimal currentUsage = getCurrentCycleUsage(subscriptionId, cyclePeriodResultVO, accountCurrency);
        BigDecimal totalUsageWithCurrent = currentUsage.add(usage);

        // 6. 计算目录价和优惠价（调用原价计算策略）
        OriginalPriceCalculateResponse calculateResponse = calculateOriginalPriceByStrategy(totalUsageWithCurrent,
                currentUsage, usage, request.getUsageUnit(), packageRateConfigDTO, packageConfigDTO, serviceConfig.getPaymentOptions(),
                serviceConfig.getCoupons(), getTaxRate(serviceInfo), request.enableTax());
        BigDecimal originalPrice = calculateResponse.getOriginalPrice();
        BigDecimal discountedPrice = calculateResponse.getDiscountedPrice();

        // 7. 计算优惠金额
        BigDecimal discountAmount = originalPrice.subtract(discountedPrice);

        // 9. 更新费率用量
        String usageUnit = request.getUsageUnit();
        Boolean inTrail = calculateResponse.getInTrail();
        boolean updateSuccess = incomeRateUsageManageService.updateRateUsage(cyclePeriodResultVO, usage, usageUnit, inTrail);
        if (!updateSuccess) {
            log.error("套餐费率用量更新失败");
            throw new BillingFailException(ErrorCodeConstants.BILLING_CALCULATION_FAILED, "用量更新失败");
        }

        // 10. 更新缓存中的套餐用量
        // BigDecimal packageUsage = rateCacheDataService.addRateUsage(BillTypeEnum.INCOME, ChargeRateTypeEnum.PACKAGE,
        //         subscriptionId, customerAccountVO.getAccountId(), serviceConfig.getServiceId(),
        //         generateBillingCycle(cyclePeriodResultVO), usage, usageUnit, billingLockTime, inTrail);
        // log.debug("套餐费率用量更新成功，累计用量: {}", packageUsage);

        // 11. 构建响应结果
        IncomeRateChargeResponse response = IncomeRateChargeResponse.success();
        response.setSuccess(true);
        response.setCurrency(packageConfigDTO.getCurrency());
        response.setUsage(usage);
        response.setUsageUnit(usageUnit);
        response.setCyclePeriodResultVO(cyclePeriodResultVO);
        response.setRateConfig(packageRateConfigDTO);
        response.setCouponList(serviceConfig.getCoupons());
        convertChargeResponse(response, calculateResponse);

        log.info("套餐计费完成，累计用量: {}, 本次用量: {}, 目录价: {}, 优惠后价格: {}, 优惠金额: {}",
                totalUsageWithCurrent, usage, originalPrice, discountedPrice, discountAmount);

        return response;
    }

    /**
     * 获取当前周期累计用量
     */
    private BigDecimal getCurrentCycleUsage(Long subscriptionId, CyclePeriodResultVO cyclePeriodResult, String currency) {
        if (!cyclePeriodResult.isSuccess()) {
            log.warn("周期计算失败，返回0用量");
            return BigDecimal.ZERO;
        }

        String billingCycle = cyclePeriodResult.getBillingCycle();
        return incomeRateUsageManageService.getRateUsage(
                ChargeRateTypeEnum.PACKAGE.getType(),
                subscriptionId,
                null,
                null,
                billingCycle,
                currency
        );
    }

    /**
     * 通过策略计算套餐费率目录价和优惠价
     *
     * @param totalUsageWithCurrent 包含本次用量的总累计用量
     * @param previousUsage         之前的累计用量
     * @param currentUsage          本次用量
     * @param rateConfig            套餐费率配置
     * @param packageConfig         套餐配置
     * @param paymentOptions        支付方式 0-现金 1-积分
     * @param couponList            优惠券列表
     * @return 本次计费金额
     */
    private OriginalPriceCalculateResponse calculateOriginalPriceByStrategy(BigDecimal totalUsageWithCurrent,
                                                                            BigDecimal previousUsage,
                                                                            BigDecimal currentUsage,
                                                                            String currentUsageUnit,
                                                                            PackageRateConfigDTO rateConfig,
                                                                            PackageRateConfigDTO.PackageConfigDTO packageConfig,
                                                                            Integer paymentOptions,
                                                                            List<Coupon> couponList,
                                                                            BigDecimal taxRate,
                                                                            boolean enableTax) {
        // 构建原价计算请求
        OriginalPriceCalculateRequest request = new OriginalPriceCalculateRequest();
        request.setTotalUsageWithCurrent(totalUsageWithCurrent);
        request.setPreviousUsage(previousUsage);
        request.setCurrentUsage(currentUsage);
        request.setCurrentUsageUnit(currentUsageUnit);
        request.setRateConfig(rateConfig);
        request.setPackageConfig(packageConfig);
        request.setPaymentOptions(paymentOptions);
        request.setCouponList(couponList);
        request.setTaxRate(taxRate);
        request.setCalculateTaxEnabled(enableTax);

        // 调用原价计算策略
        OriginalPriceCalculateResponse response = strategyFactory.execute(OriginalPriceRateTypeEnum.class,
                OriginalPriceRateTypeEnum.PACKAGE.name(), request);
        if (response.getSuccess()) {
            return response;
        }
        log.error("response: {}, 原价计算失败", response);
        throw new BillingFailException(ErrorCodeConstants.BILLING_CALCULATION_FAILED, response.getErrorMessage());
    }

}
