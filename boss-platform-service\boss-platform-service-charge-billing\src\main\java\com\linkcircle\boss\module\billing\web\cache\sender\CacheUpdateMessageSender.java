package com.linkcircle.boss.module.billing.web.cache.sender;

import com.linkcircle.boss.framework.common.constants.ChargeTopicConstant;
import com.linkcircle.boss.module.billing.api.cache.dto.CacheUpdateMessageDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025-07-24 10:50
 * @description 缓存更新消息发送器 - RocketMQ版本
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class CacheUpdateMessageSender {

    private final RocketMQTemplate rocketMQTemplate;

    /**
     * 发送缓存更新消息
     */
    public void sendCacheUpdateMessage(String businessType, String businessId) {
        try {
            CacheUpdateMessageDTO message = CacheUpdateMessageDTO.create(businessType, businessId);

            String destination = ChargeTopicConstant.CHARGE_BROADCAST_TOPIC + ":" + ChargeTopicConstant.TAG_CACHE_UPDATE;

            rocketMQTemplate.convertAndSend(destination, message);

            log.debug("发送缓存更新消息成功: businessType={}, businessId={}", businessType, businessId);
        } catch (Exception e) {
            log.error("发送缓存更新消息失败: businessType={}, businessId={}", businessType, businessId, e);
        }
    }

    /**
     * 发送客户账户缓存更新消息
     */
    public void sendCustomerAccountUpdate(Long accountId) {
        sendCacheUpdateMessage(CacheUpdateMessageDTO.Type.CUSTOMER_ACCOUNT, String.valueOf(accountId));
    }

    /**
     * 发送供应商账户缓存更新消息
     */
    public void sendSupplierAccountUpdate(Long accountId) {
        sendCacheUpdateMessage(CacheUpdateMessageDTO.Type.SUPPLIER_ACCOUNT, String.valueOf(accountId));
    }

    /**
     * 发送秘钥缓存更新消息
     */
    public void sendSecretKeyUpdate(String appId) {
        sendCacheUpdateMessage(CacheUpdateMessageDTO.Type.SECRET_KEY, appId);
    }

    /**
     * 发送订阅缓存更新消息
     */
    public void sendSubscriptionUpdate(Long accountId) {
        sendCacheUpdateMessage(CacheUpdateMessageDTO.Type.ACCOUNT_SUBSCRIPTION, String.valueOf(accountId));
    }

    /**
     * 发送资源采购缓存更新消息
     */
    public void sendResourcePurchaseUpdate(Long accountId) {
        sendCacheUpdateMessage(CacheUpdateMessageDTO.Type.ACCOUNT_RESOURCE_PURCHASE, String.valueOf(accountId));
    }

    /**
     * 发送指标单位缓存更新消息
     */
    public void sendMetricUnitUpdate() {
        sendCacheUpdateMessage(CacheUpdateMessageDTO.Type.METRIC_UNIT, CacheUpdateMessageDTO.Type.METRIC_UNIT);
    }

    /**
     * 发送规模缓存更新消息
     */
    public void sendScaleUpdate(String scaleKey) {
        sendCacheUpdateMessage(CacheUpdateMessageDTO.Type.SCALE, scaleKey);
    }
}
